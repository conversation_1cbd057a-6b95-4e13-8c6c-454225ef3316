import {
  Preset,
  defineConfig,
  presetAttributify,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup,
} from "unocss";

import {
  presetApplet,
  presetRemRpx,
  transformerApplet,
  transformerAttributify,
} from "unocss-applet";

const isH5 = process.env?.UNI_PLATFORM === "h5";
const isMp = process.env?.UNI_PLATFORM?.startsWith("mp") ?? false;

const presets: Preset[] = [];
if (!isMp) {
  /**
   * you can add `presetAttributify()` here to enable unocss attributify mode prompt
   * although preset is not working for applet, but will generate useless css
   * 为了不生产无用的css,要过滤掉 applet
   */
  // 支持css class属性化，eg: `<button bg="blue-400 hover:blue-500 dark:blue-500 dark:hover:blue-600" text="sm white">attributify Button</button>`
  presets.push(presetAttributify());
}
if (!isH5) {
  presets.push(presetRemRpx());
}
export default defineConfig({
  presets: [
    presetApplet({ enable: !isH5 }),
    ...presets,
    // 支持图标，需要搭配图标库，eg: @iconify-json/carbon, 使用 `<button class="i-carbon-sun dark:i-carbon-moon" />`
    presetIcons({
      scale: 1.2,
      warn: true,
      extraProperties: {
        display: "inline-block",
        "vertical-align": "middle",
      },
    }),
  ],
  /**
   * 自定义快捷语句
   * @see https://github.com/unocss/unocss#shortcuts
   */
  shortcuts: [["center", "flex justify-center items-center"]],
  transformers: [
    // 启用 @apply 功能
    transformerDirectives(),
    // 启用 () 分组功能
    // 支持css class组合，eg: `<div class="hover:(bg-gray-400 font-medium) font-(light mono)">测试 unocss</div>`
    transformerVariantGroup(),
    // Don't change the following order
    transformerAttributify({
      // 解决与第三方框架样式冲突问题
      prefixedOnly: true,
      prefix: "fg",
    }),
    transformerApplet(),
  ],
  rules: [
    ["text-base", { color: "#121212" }],
    ["text-333", { color: "#333333" }],
    ["text-555", { color: "#505050" }],
    ["text-666", { color: "#666666" }],
    ["text-999", { color: "#999999" }],
    ["text-ccc", { color: "#cccccc" }],
    ["text-eee", { color: "#eeeeee" }],
    ["text-f5", { color: "#f5f5f5" }],
    ["text-f7", { color: "#f7f7f7" }],
    ["text-f0", { color: "#f0f0f0" }],
    [
      "flex-x",
      {
        "display": "flex",
        "flex-direction": "row",
        "flex-wrap": "wrap",
        "flex-grow": 1,
        "flex-shrink": 1,
        "flex-basis": "auto",
      },
    ],
    [
      "flex-y",
      {
        "display": "flex",
        "flex-direction": "column",
      },
    ],
    [
      "flex-x-center",
      {
        display: "flex",
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
      },
    ],
    [
      "flex-x-between",
      {
        "display": "flex",
        "flex-direction": "row",
        "justify-content": "space-between",
        "align-items": "center",
      },
    ],
    [
      "flex-x-around",
      {
        "display": "flex",
        "flex-direction": "row",
        "justify-content": "space-around",
        "align-items": "center",
      },
    ],
    [
      "flex-y-center",
      {
        "display": "flex",
        "flex-direction": "column",
        "justify-content": "center",
        "align-items": "center",
      },
    ],
    [
      "flex-y-between",
      {
        "display": "flex",
        "flex-direction": "column",
        "justify-content": "space-between",
        "align-items": "center",
      },
    ],

    ["pt-safe", { "padding-top": "env(safe-area-inset-top)" }],
    ["pb-safe", { "padding-bottom": "env(safe-area-inset-bottom)" }],
    ["ft12", { "font-size": "24rpx" }],
    ["ft14", { "font-size": "28rpx" }],
    ["ft16", { "font-size": "32rpx" }],
    ["ft18", { "font-size": "36rpx" }],
    ["ft20", { "font-size": "40rpx" }],
    ["ft22", { "font-size": "44rpx" }],
    ["ft24", { "font-size": "48rpx" }],
    ["ft26", { "font-size": "52rpx" }],
    ["ft28", { "font-size": "56rpx" }],
    ["ft30", { "font-size": "60rpx" }],
    ["ft32", { "font-size": "64rpx" }],
    ["ft34", { "font-size": "68rpx" }],
    ["ft36", { "font-size": "72rpx" }],
    ["spacing-4", { "margin-bottom": "8rpx" }],
    ["spacing-6", { "margin-bottom": "12rpx" }],
    ["spacing-8", { "margin-bottom": "16rpx" }],
    ["spacing-10", { "margin-bottom": "20rpx" }],
    ["spacing-12", { "margin-bottom": "24rpx" }],
    ["spacing-14", { "margin-bottom": "28rpx" }],
    ["spacing-16", { "margin-bottom": "32rpx" }],
    ["spacing-18", { "margin-bottom": "36rpx" }],
    ["spacing-20", { "margin-bottom": "40rpx" }],
    ["spacing-24", { "margin-bottom": "48rpx" }],
    ["text-main", { color: "$text-main" }],
    ["text-inverse", { color: "$text-inverse" }],
    ["text-grey", { color: "$text-grey" }],
    ["text-placeholder", { color: "$text-placeholder" }],
    ["text-disable", { color: "$text-disable" }],
    ["bg-base", { "background-color": "$bg" }],
  ],
});

/**
 * 最终这一套组合下来会得到：
 * mp 里面：mt-4 => margin-top: 32rpx
 * h5 里面：mt-4 => margin-top: 1rem
 */
