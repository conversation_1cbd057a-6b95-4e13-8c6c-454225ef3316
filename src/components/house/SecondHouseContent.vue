<template>
  <view class="second-house-content">
    <!-- 精选二手房 -->
    <view class="featured-houses bg-white mb-20rpx">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">精选二手房</text>
        <view class="flex items-center" @tap="navigateToList('featured')">
          <text class="text-26rpx color-grey">更多房源</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>

      <!-- 二手房卡片列表 -->
      <view class="second-house-list px-20rpx pb-20rpx">
        <view
          v-for="(house, index) in featuredHouses"
          :key="index"
          class="second-house-card mb-20rpx bg-white rounded-lg overflow-hidden shadow-sm"
          @tap="navigateToDetail(house.id)"
        >
          <view class="flex p-20rpx">
            <image
              :src="house.image"
              mode="aspectFill"
              class="house-image rounded-lg"
            ></image>
            <view class="house-info flex-1 ml-20rpx">
              <text class="house-title text-32rpx font-bold line-clamp-1">{{
                house.title
              }}</text>
              <view class="flex items-center mt-10rpx">
                <text class="house-layout text-26rpx">{{ house.layout }}</text>
                <text class="house-area text-26rpx ml-20rpx"
                  >{{ house.area }}㎡</text
                >
              </view>
              <view class="flex flex-wrap mt-10rpx">
                <text
                  v-for="(tag, tIndex) in house.tags"
                  :key="tIndex"
                  class="house-tag mr-10rpx mb-10rpx"
                  >{{ tag }}</text
                >
              </view>
              <view class="flex justify-between items-center mt-10rpx">
                <text class="house-price text-32rpx color-red font-bold"
                  >{{ house.price }}万</text
                >
                <text class="house-unit-price text-24rpx color-grey"
                  >{{ house.unitPrice }}元/㎡</text
                >
              </view>
            </view>
          </view>
          <view class="house-location flex items-center px-20rpx pb-20rpx">
            <text class="i-carbon-location text-24rpx color-grey"></text>
            <text
              class="location-text text-24rpx color-grey ml-10rpx line-clamp-1"
              >{{ house.location }}</text
            >
          </view>
        </view>
      </view>
    </view>

    <!-- 降价房源 -->
    <view class="price-cut-houses bg-white mb-20rpx">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <view class="flex items-center">
          <text class="text-32rpx font-bold">降价房源</text>
          <text class="price-cut-badge ml-10rpx">捡漏必看</text>
        </view>
        <view class="flex items-center" @tap="navigateToList('price_cut')">
          <text class="text-26rpx color-grey">更多降价</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>

      <view class="price-cut-list px-20rpx pb-20rpx">
        <view
          v-for="(house, index) in priceCutHouses"
          :key="index"
          class="price-cut-item flex p-20rpx mb-20rpx bg-white rounded-lg shadow-sm"
          @tap="navigateToDetail(house.id)"
        >
          <image
            :src="house.image"
            mode="aspectFill"
            class="cut-image rounded-lg"
          ></image>
          <view class="cut-info ml-20rpx flex-1">
            <view class="flex justify-between items-start">
              <text class="text-30rpx font-bold line-clamp-1">{{
                house.title
              }}</text>
              <view
                class="price-cut-tag bg-red-100 text-red-600 text-22rpx px-10rpx py-4rpx rounded"
                >降价{{ house.priceCut }}万</view
              >
            </view>
            <view class="mt-16rpx">
              <text class="text-26rpx"
                >{{ house.layout }} | {{ house.area }}㎡ |
                {{ house.direction }}</text
              >
            </view>
            <view class="mt-16rpx flex items-center">
              <text class="i-carbon-location text-24rpx color-grey"></text>
              <text class="text-26rpx color-grey ml-6rpx">{{
                house.location
              }}</text>
            </view>
            <view class="mt-16rpx flex items-center justify-between">
              <view>
                <text class="text-28rpx color-red font-bold"
                  >{{ house.currentPrice }}万</text
                >
                <text class="text-24rpx color-grey ml-10rpx line-through"
                  >{{ house.originalPrice }}万</text
                >
              </view>
              <text class="text-24rpx color-grey"
                >{{ house.unitPrice }}元/㎡</text
              >
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 学区房专区 -->
    <view class="school-houses bg-white mb-20rpx">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">学区房专区</text>
        <view class="flex items-center" @tap="navigateToList('school')">
          <text class="text-26rpx color-grey">更多学区房</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>

      <view class="school-grid px-20rpx pb-20rpx grid grid-cols-2 gap-20rpx">
        <view
          v-for="(school, index) in schoolHouses"
          :key="index"
          class="school-card bg-white rounded-lg overflow-hidden shadow-sm"
          @tap="navigateToDetail(school.id)"
        >
          <image
            :src="school.image"
            mode="aspectFill"
            class="school-image"
          ></image>
          <view class="school-info p-16rpx">
            <view class="flex justify-between items-center">
              <text class="text-28rpx font-bold line-clamp-1">{{
                school.title
              }}</text>
              <text class="text-24rpx color-primary">{{
                school.schoolName
              }}</text>
            </view>
            <view class="flex items-center mt-10rpx">
              <text class="text-24rpx color-grey"
                >{{ school.layout }} | {{ school.area }}㎡</text
              >
            </view>
            <view class="flex items-center justify-between mt-10rpx">
              <text class="text-28rpx color-red font-bold"
                >{{ school.price }}万</text
              >
              <text
                class="text-22rpx bg-primary-50 color-primary px-10rpx py-4rpx rounded"
                >{{ school.distance }}</text
              >
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";

// 精选二手房数据
const featuredHouses = ref([
  {
    id: "second1",
    title: "阳光花园三室两厅",
    layout: "3室2厅1卫",
    area: "108",
    price: "89.5",
    unitPrice: "8287",
    tags: ["满二年", "近地铁", "南北通透"],
    location: "城南区 华兴路88号 阳光花园小区",
    image: "https://picsum.photos/seed/second1/300/200",
  },
  {
    id: "second2",
    title: "金源小区精装修两居室",
    layout: "2室1厅1卫",
    area: "76",
    price: "62.8",
    unitPrice: "8263",
    tags: ["精装修", "拎包入住", "低总价"],
    location: "城东区 东风路128号 金源小区",
    image: "https://picsum.photos/seed/second2/300/200",
  },
]);

// 降价房源数据
const priceCutHouses = ref([
  {
    id: "cut1",
    title: "山水花园南向三居室",
    layout: "3室2厅2卫",
    area: "120",
    direction: "南北通透",
    currentPrice: "95.8",
    originalPrice: "103.5",
    priceCut: "7.7",
    unitPrice: "7983",
    location: "城南区 文化路36号 山水花园",
    image: "https://picsum.photos/seed/cut1/200/150",
  },
  {
    id: "cut2",
    title: "城中央小区大户型",
    layout: "4室2厅2卫",
    area: "156",
    direction: "东南向",
    currentPrice: "128.5",
    originalPrice: "136",
    priceCut: "7.5",
    unitPrice: "8237",
    location: "城中区 人民路99号 城中央小区",
    image: "https://picsum.photos/seed/cut2/200/150",
  },
]);

// 学区房数据
const schoolHouses = ref([
  {
    id: "school1",
    title: "绿洲家园两居室",
    layout: "2室1厅1卫",
    area: "85",
    price: "76.5",
    schoolName: "实验小学",
    distance: "学校500m",
    image: "https://picsum.photos/seed/school1/400/300",
  },
  {
    id: "school2",
    title: "阳光南区精装修三居",
    layout: "3室2厅1卫",
    area: "110",
    price: "98.8",
    schoolName: "市一中",
    distance: "学校800m",
    image: "https://picsum.photos/seed/school2/400/300",
  },
]);

// 导航到详情页
const navigateToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/secondHouse/detail?id=${id}`,
  });
};

// 导航到列表页面
const navigateToList = (filter: string = "") => {
  uni.navigateTo({
    url: `/pages/house/secondHouse/list${filter ? "?filter=" + filter : ""}`,
  });
};
</script>

<style lang="scss" scoped>
.second-house-content {
  background-color: #f8f9fa;
}

.house-image {
  width: 220rpx;
  height: 160rpx;
}

.house-tag {
  padding: 4rpx 12rpx;
  background-color: #f0f0f0;
  color: #666;
  border-radius: 6rpx;
  font-size: 22rpx;
}

.price-cut-badge {
  font-size: 22rpx;
  color: #fff;
  background-color: $primary;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
}

.cut-image {
  width: 200rpx;
  height: 150rpx;
}

.school-card {
  overflow: hidden;
}

.school-image {
  width: 100%;
  height: 180rpx;
}
</style>
