<template>
  <view class="commercial-house-content">
    <!-- 商铺专区 -->
    <view class="shop-section bg-white mb-20rpx">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">热门商铺</text>
        <view class="flex items-center" @tap="navigateToList('shop')">
          <text class="text-26rpx color-grey">更多商铺</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>

      <!-- 商铺列表 -->
      <scroll-view scroll-x class="scroll-view-x pb-20rpx">
        <view class="shop-list flex px-20rpx">
          <view
            v-for="(shop, index) in shopList"
            :key="index"
            class="shop-card mr-20rpx"
            @tap="navigateToDetail(shop.id, 'shop')"
          >
            <image
              :src="shop.image"
              mode="aspectFill"
              class="shop-image rounded-t-lg"
            ></image>
            <view class="shop-info p-16rpx bg-white">
              <text class="shop-name text-30rpx font-bold line-clamp-1">{{
                shop.name
              }}</text>
              <view class="flex items-center mt-10rpx">
                <text class="shop-area text-26rpx color-grey"
                  >{{ shop.area }}㎡</text
                >
              </view>
              <view class="flex items-center justify-between mt-10rpx">
                <text class="shop-price text-28rpx color-red font-bold">{{
                  shop.price
                }}</text>
                <view
                  class="shop-type bg-primary-50 color-primary text-22rpx px-10rpx py-4rpx rounded"
                  >{{ shop.type }}</view
                >
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 写字楼专区 -->
    <view class="office-section bg-white mb-20rpx">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">精选写字楼</text>
        <view class="flex items-center" @tap="navigateToList('office')">
          <text class="text-26rpx color-grey">更多写字楼</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>

      <!-- 写字楼列表 -->
      <view class="office-list px-20rpx pb-20rpx">
        <view
          v-for="(office, index) in officeList"
          :key="index"
          class="office-card bg-white rounded-lg overflow-hidden shadow-sm mb-20rpx"
          @tap="navigateToDetail(office.id, 'office')"
        >
          <view class="flex">
            <image
              :src="office.image"
              mode="aspectFill"
              class="office-image"
            ></image>
            <view class="office-info p-20rpx flex-1">
              <text class="office-name text-32rpx font-bold line-clamp-1">{{
                office.name
              }}</text>
              <view class="flex items-center mt-10rpx">
                <text
                  class="office-location text-26rpx color-grey line-clamp-1"
                  >{{ office.location }}</text
                >
              </view>
              <view class="flex flex-wrap mt-10rpx">
                <text
                  v-for="(tag, tIndex) in office.tags"
                  :key="tIndex"
                  class="office-tag mr-10rpx mb-10rpx"
                  >{{ tag }}</text
                >
              </view>
              <view class="flex justify-between items-center mt-10rpx">
                <text class="office-price text-28rpx color-red font-bold">{{
                  office.price
                }}</text>
                <text class="office-area text-24rpx color-grey"
                  >{{ office.area }}㎡</text
                >
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 厂房仓库 -->
    <view class="factory-warehouse bg-white">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">厂房仓库</text>
        <view class="flex items-center" @tap="navigateToList('factory')">
          <text class="text-26rpx color-grey">查看更多</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>

      <!-- 厂房仓库列表 -->
      <view class="factory-list px-20rpx pb-20rpx grid grid-cols-2 gap-20rpx">
        <view
          v-for="(item, index) in factoryList"
          :key="index"
          class="factory-card bg-white rounded-lg overflow-hidden shadow-sm"
          @tap="navigateToDetail(item.id, 'factory')"
        >
          <image
            :src="item.image"
            mode="aspectFill"
            class="factory-image"
          ></image>
          <view class="factory-info p-16rpx">
            <view class="flex justify-between items-center">
              <text class="text-28rpx font-bold line-clamp-1">{{
                item.name
              }}</text>
              <view class="type-badge text-22rpx">{{ item.type }}</view>
            </view>
            <view class="flex items-center mt-10rpx">
              <text class="text-24rpx color-grey">{{ item.area }}㎡</text>
            </view>
            <view class="flex items-center justify-between mt-10rpx">
              <text class="text-28rpx color-red font-bold">{{
                item.price
              }}</text>
              <text class="text-24rpx color-grey">{{ item.location }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";

// 热门商铺数据
const shopList = ref([
  {
    id: "shop1",
    name: "步行街核心地带商铺",
    area: "68",
    price: "8500元/月",
    type: "临街门面",
    image: "https://picsum.photos/seed/shop1/400/300",
  },
  {
    id: "shop2",
    name: "万达广场一楼商铺",
    area: "42",
    price: "12800元/月",
    type: "购物中心",
    image: "https://picsum.photos/seed/shop2/400/300",
  },
  {
    id: "shop3",
    name: "人民路餐饮店转让",
    area: "110",
    price: "3.8万/月",
    type: "餐饮旺铺",
    image: "https://picsum.photos/seed/shop3/400/300",
  },
]);

// 写字楼数据
const officeList = ref([
  {
    id: "office1",
    name: "城市中心金融广场",
    location: "城中区 人民路2号",
    area: "288",
    price: "35元/㎡/天",
    tags: ["5A写字楼", "豪华装修", "配套齐全"],
    image: "https://picsum.photos/seed/office1/300/200",
  },
  {
    id: "office2",
    name: "商贸大厦写字间",
    location: "城东区 东风路88号",
    area: "120",
    price: "22元/㎡/天",
    tags: ["地铁口", "精装修", "带家具"],
    image: "https://picsum.photos/seed/office2/300/200",
  },
]);

// 厂房仓库数据
const factoryList = ref([
  {
    id: "factory1",
    name: "标准化厂房",
    area: "1200",
    price: "16元/㎡/月",
    type: "厂房",
    location: "工业园区",
    image: "https://picsum.photos/seed/factory1/400/300",
  },
  {
    id: "factory2",
    name: "物流仓储中心",
    area: "800",
    price: "13元/㎡/月",
    type: "仓库",
    location: "物流园区",
    image: "https://picsum.photos/seed/factory2/400/300",
  },
  {
    id: "factory3",
    name: "新建独栋厂房",
    area: "3600",
    price: "18元/㎡/月",
    type: "厂房",
    location: "高新区",
    image: "https://picsum.photos/seed/factory3/400/300",
  },
  {
    id: "factory4",
    name: "冷链仓储设施",
    area: "560",
    price: "22元/㎡/月",
    type: "仓库",
    location: "城北物流园",
    image: "https://picsum.photos/seed/factory4/400/300",
  },
]);

// 导航到详情页
const navigateToDetail = (id: string, type: string) => {
  uni.navigateTo({
    url: `/pages/house/commercial/detail?id=${id}&type=${type}`,
  });
};

// 导航到列表页面
const navigateToList = (type: string = "") => {
  uni.navigateTo({
    url: `/pages/house/commercial/list${type ? "?type=" + type : ""}`,
  });
};
</script>

<style lang="scss" scoped>
.commercial-house-content {
  background-color: #f8f9fa;
}

.scroll-view-x {
  white-space: nowrap;
}

.shop-card {
  width: 280rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.shop-image {
  width: 280rpx;
  height: 200rpx;
}

.office-image {
  width: 220rpx;
  height: 160rpx;
}

.office-tag {
  padding: 4rpx 12rpx;
  background-color: #f0f0f0;
  color: #666;
  border-radius: 6rpx;
  font-size: 22rpx;
}

.factory-image {
  width: 100%;
  height: 180rpx;
}

.type-badge {
  background-color: rgba($primary, 0.1);
  color: $primary;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
}
</style>
