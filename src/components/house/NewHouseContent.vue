<template>
  <view class="new-house-content">
    <!-- 热门楼盘 -->
    <view class="hot-projects bg-white mb-20rpx">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">热门楼盘</text>
        <view class="flex items-center" @tap="navigateToList('hot')">
          <text class="text-26rpx color-grey">更多楼盘</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>

      <!-- 热门楼盘列表 -->
      <scroll-view scroll-x class="scroll-view-x pb-20rpx">
        <view class="project-list flex px-20rpx">
          <view
            v-for="(project, index) in hotProjects"
            :key="index"
            class="project-card mr-20rpx"
            @tap="navigateToDetail(project.id)"
          >
            <image
              :src="project.image"
              mode="aspectFill"
              class="project-image rounded-lg"
            ></image>
            <view class="project-info p-16rpx">
              <text class="project-name text-30rpx font-bold line-clamp-1">{{
                project.name
              }}</text>
              <view class="flex items-center mt-10rpx">
                <text class="location text-26rpx color-grey line-clamp-1">{{
                  project.location
                }}</text>
              </view>
              <view class="flex items-center justify-between mt-10rpx">
                <text class="price text-28rpx color-red font-bold">{{
                  project.price
                }}</text>
                <view
                  class="label-box bg-primary-50 text-primary text-22rpx px-10rpx py-4rpx rounded"
                  >{{ project.labelText }}</view
                >
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 本月开盘 -->
    <view class="new-open-projects bg-white mb-20rpx">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">本月开盘</text>
        <view class="flex items-center" @tap="navigateToList('month')">
          <text class="text-26rpx color-grey">更多开盘</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>

      <!-- 开盘项目列表 -->
      <view class="project-grid px-20rpx pb-20rpx grid grid-cols-2 gap-20rpx">
        <view
          v-for="(project, index) in openProjects"
          :key="index"
          class="opening-card rounded-lg overflow-hidden shadow-sm"
          @tap="navigateToDetail(project.id)"
        >
          <image
            :src="project.image"
            mode="aspectFill"
            class="opening-image"
          ></image>
          <view class="opening-info p-16rpx bg-white">
            <text class="project-name text-28rpx font-bold line-clamp-1">{{
              project.name
            }}</text>
            <view class="flex items-center mt-8rpx">
              <text class="location text-24rpx color-grey line-clamp-1">{{
                project.location
              }}</text>
            </view>
            <view class="flex items-center justify-between mt-8rpx">
              <text class="price text-28rpx color-red font-bold">{{
                project.price
              }}</text>
              <text class="open-date text-22rpx color-primary">{{
                project.openDate
              }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 小户型推荐 -->
    <view class="small-apartments bg-white mb-20rpx">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">小户型推荐</text>
        <view class="flex items-center" @tap="navigateToList('small')">
          <text class="text-26rpx color-grey">查看更多</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>

      <!-- 小户型列表 -->
      <view class="small-list px-20rpx pb-20rpx">
        <view
          v-for="(item, index) in smallApartments"
          :key="index"
          class="small-item bg-white rounded-lg shadow-sm p-20rpx mb-20rpx flex"
          @tap="navigateToDetail(item.id)"
        >
          <image
            :src="item.image"
            mode="aspectFill"
            class="small-image rounded-lg"
          ></image>
          <view class="small-info ml-20rpx flex-1">
            <view class="flex justify-between items-center">
              <text class="project-name text-30rpx font-bold line-clamp-1">{{
                item.name
              }}</text>
              <text class="price text-28rpx color-red font-bold">{{
                item.price
              }}</text>
            </view>
            <view class="mt-16rpx flex items-center">
              <text class="location text-26rpx color-grey">{{
                item.location
              }}</text>
            </view>
            <view class="mt-16rpx flex flex-wrap">
              <text
                v-for="(tag, tIndex) in item.tags"
                :key="tIndex"
                class="house-tag mr-10rpx mb-10rpx"
                >{{ tag }}</text
              >
            </view>
            <view class="mt-16rpx">
              <text class="text-26rpx"
                >{{ item.area }}㎡ | {{ item.layout }}</text
              >
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";

// 热门楼盘数据
const hotProjects = ref([
  {
    id: "new1",
    name: "碧桂园·蓝湾南岸",
    location: "城南区 · 滨江花园",
    price: "6800元/㎡起",
    labelText: "近地铁",
    image: "https://picsum.photos/seed/newhouse1/400/300",
  },
  {
    id: "new2",
    name: "恒大·城市之光",
    location: "城东区 · 科技新城",
    price: "7300元/㎡起",
    labelText: "学区房",
    image: "https://picsum.photos/seed/newhouse2/400/300",
  },
  {
    id: "new3",
    name: "万科·城市之窗",
    location: "城西区 · 商务中心",
    price: "8100元/㎡起",
    labelText: "公园旁",
    image: "https://picsum.photos/seed/newhouse3/400/300",
  },
  {
    id: "new4",
    name: "绿地·未来城",
    location: "城北区 · 新区中心",
    price: "6900元/㎡起",
    labelText: "品牌地产",
    image: "https://picsum.photos/seed/newhouse4/400/300",
  },
]);

// 本月开盘数据
const openProjects = ref([
  {
    id: "open1",
    name: "恒大·翡翠华庭",
    location: "城南区 · 山水新城",
    price: "7200元/㎡起",
    openDate: "6月20日开盘",
    image: "https://picsum.photos/seed/openhouse1/400/300",
  },
  {
    id: "open2",
    name: "华润·置地公馆",
    location: "城东区 · 东风广场",
    price: "8500元/㎡起",
    openDate: "6月28日开盘",
    image: "https://picsum.photos/seed/openhouse2/400/300",
  },
]);

// 小户型推荐数据
const smallApartments = ref([
  {
    id: "small1",
    name: "龙湖·小户型公寓",
    location: "城中区 · 商业中心",
    price: "6500元/㎡起",
    area: "48",
    layout: "1室1厅1卫",
    tags: ["小户型", "精装修", "拎包入住"],
    image: "https://picsum.photos/seed/smallhouse1/200/150",
  },
  {
    id: "small2",
    name: "保利·青年公寓",
    location: "城东区 · 大学城",
    price: "6200元/㎡起",
    area: "52",
    layout: "1室1厅1卫",
    tags: ["交通便利", "配套齐全", "首付低"],
    image: "https://picsum.photos/seed/smallhouse2/200/150",
  },
]);

// 导航到详情页
const navigateToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/newHouse/detail?id=${id}`,
  });
};

// 导航到列表页面
const navigateToList = (filter: string = "") => {
  uni.navigateTo({
    url: `/pages/house/newHouse/list${filter ? "?filter=" + filter : ""}`,
  });
};
</script>

<style lang="scss" scoped>
.new-house-content {
  background-color: #f8f9fa;
}

.scroll-view-x {
  white-space: nowrap;
}

.project-card {
  width: 280rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.project-image {
  width: 280rpx;
  height: 200rpx;
}

.opening-card {
  background-color: #ffffff;
  overflow: hidden;
}

.opening-image {
  width: 100%;
  height: 200rpx;
}

.small-image {
  width: 200rpx;
  height: 160rpx;
}

.house-tag {
  padding: 4rpx 12rpx;
  background-color: #f0f0f0;
  color: #666;
  border-radius: 6rpx;
  font-size: 22rpx;
}

.label-box {
  border-radius: 4rpx;
}
</style>
