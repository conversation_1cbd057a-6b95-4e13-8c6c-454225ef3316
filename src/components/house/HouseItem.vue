<template>
  <view
    class="house-item bg-white flex mb-20rpx rounded-lg"
    @tap="navigateToDetail"
  >
    <view class="house-image-container mr-20rpx relative">
      <image
        :src="house.image"
        class="house-image rounded-lg"
        mode="aspectFill"
      ></image>
      <!-- 视频标记 -->
      <view v-if="house.hasVideo" class="video-mark">
        <text class="i-carbon-play-filled text-white text-36rpx"></text>
      </view>
      <!-- VR标记 -->
      <view v-if="house.hasVR" class="vr-mark">
        <text class="text-white text-24rpx">VR</text>
      </view>
    </view>
    <view class="house-info flex-1 py-20rpx pr-20rpx overflow-hidden">
      <!-- 标题 -->
      <view class="title text-32rpx font-bold line-clamp-1">{{
        house.title || house.name
      }}</view>

      <!-- 房源详情 -->
      <view class="info text-28rpx color-grey mt-10rpx line-clamp-1">
        <template v-if="type === 'second' || type === 'rent'">
          <text>{{ house.layout || getLayoutText() }}</text>
          <text v-if="house.area"> | {{ house.area }}㎡</text>
          <text v-if="house.direction"> | {{ house.direction }}</text>
          <text v-if="house.floor"> | {{ house.floor }}</text>
          <text v-if="type === 'rent' && house.rentType">
            | {{ house.rentType }}</text
          >
        </template>
        <template v-else-if="type === 'new'">
          <text>{{ house.location || house.address }}</text>
        </template>
        <template v-else-if="type === 'commercial'">
          <text>{{ house.area }}㎡</text>
          <text v-if="house.type"> | {{ house.type }}</text>
          <text v-if="house.location"> | {{ house.location }}</text>
        </template>
        <template v-else>
          {{ house.info }}
        </template>
      </view>

      <!-- 标签 -->
      <view
        v-if="house.tags && house.tags.length"
        class="tags flex flex-wrap mt-10rpx"
      >
        <text
          v-for="(tag, index) in house.tags"
          :key="index"
          class="tag-item mr-10rpx mb-10rpx"
          >{{ tag }}</text
        >
      </view>

      <!-- 价格区域 -->
      <view class="price-line flex justify-between items-center mt-10rpx">
        <view>
          <text class="price text-32rpx font-bold" :class="priceColorClass">{{
            formattedPrice
          }}</text>
          <text
            v-if="house.unitPrice"
            class="unit-price text-24rpx color-grey ml-10rpx"
            >{{ house.unitPrice }}</text
          >
        </view>

        <!-- 额外信息：比如关注人数、发布时间等 -->
        <view v-if="house.extraInfo" class="extra-info text-24rpx color-grey">
          {{ house.extraInfo }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";

// 定义属性
const props = defineProps({
  // 房源信息
  house: {
    type: Object,
    required: true,
  },
  // 房源类型：second(二手房)、new(新房)、rent(租房)、commercial(商铺办公)
  type: {
    type: String,
    default: "second",
  },
});

// 价格颜色类
const priceColorClass = computed(() => {
  if (props.type === "rent") {
    return "color-orange";
  } else if (props.type === "commercial") {
    return "color-blue";
  } else {
    return "color-red";
  }
});

// 格式化价格
const formattedPrice = computed(() => {
  if (!props.house.price) return "";

  // 如果已经是格式化的价格字符串，直接返回
  if (typeof props.house.price === "string") {
    return props.house.price;
  }

  // 根据类型格式化价格
  switch (props.type) {
    case "second":
      return `${props.house.price}万`;
    case "new":
      return `${props.house.price}元/㎡起`;
    case "rent":
      return `${props.house.price}元/月`;
    case "commercial":
      return props.house.priceType === "sale"
        ? `${props.house.price}万`
        : `${props.house.price}元/月`;
    default:
      return `${props.house.price}`;
  }
});

// 从已有属性获取户型文本
const getLayoutText = () => {
  const { rooms, halls } = props.house;
  if (rooms && halls) {
    return `${rooms}室${halls}厅`;
  }
  return "";
};

// 导航到详情页
const navigateToDetail = () => {
  const typeMap = {
    second: "secondHouse",
    new: "newHouse",
    rent: "rent",
    commercial: "commercial",
  };

  const pageType = typeMap[props.type] || props.type;
  uni.navigateTo({
    url: `/pages/house/${pageType}/detail?id=${props.house.id}`,
  });
};
</script>

<style lang="scss" scoped>
.house-item {
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.house-image-container {
  position: relative;
  width: 220rpx;
  height: 160rpx;
}

.house-image {
  width: 220rpx;
  height: 160rpx;
}

.video-mark {
  position: absolute;
  bottom: 10rpx;
  left: 10rpx;
  width: 50rpx;
  height: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
}

.vr-mark {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  padding: 4rpx 10rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 4rpx;
}

.tag-item {
  padding: 4rpx 12rpx;
  background-color: #f6f6f6;
  color: #666;
  border-radius: 4rpx;
  font-size: 22rpx;
}

.color-red {
  color: #fa5741;
}

.color-orange {
  color: #ff8f00;
}

.color-blue {
  color: #3b7aff;
}

.color-grey {
  color: #666;
}
</style>
