<template>
  <view class="house-filter bg-white">
    <!-- 筛选条件栏 -->
    <scroll-view scroll-x class="filter-scroll-view" show-scrollbar="false">
      <view class="filter-tabs flex px-20rpx">
        <!-- 区域筛选 -->
        <view
          class="filter-tab mr-30rpx"
          :class="{ active: activeFilter === 'area' }"
          @tap="openFilter('area')"
        >
          <text class="text-30rpx">{{ areaFilterText }}</text>
          <text
            class="i-carbon-chevron-down text-24rpx ml-8rpx transition-all"
            :class="{ 'rotate-180': activeFilter === 'area' }"
          ></text>
        </view>

        <!-- 价格筛选 -->
        <view
          class="filter-tab mr-30rpx"
          :class="{ active: activeFilter === 'price' }"
          @tap="openFilter('price')"
        >
          <text class="text-30rpx">{{ priceFilterText }}</text>
          <text
            class="i-carbon-chevron-down text-24rpx ml-8rpx transition-all"
            :class="{ 'rotate-180': activeFilter === 'price' }"
          ></text>
        </view>

        <!-- 户型筛选 -->
        <view
          class="filter-tab mr-30rpx"
          :class="{ active: activeFilter === 'houseType' }"
          @tap="openFilter('houseType')"
          v-if="showHouseTypeFilter"
        >
          <text class="text-30rpx">{{ houseTypeFilterText }}</text>
          <text
            class="i-carbon-chevron-down text-24rpx ml-8rpx transition-all"
            :class="{ 'rotate-180': activeFilter === 'houseType' }"
          ></text>
        </view>

        <!-- 租房方式筛选 -->
        <view
          class="filter-tab mr-30rpx"
          :class="{ active: activeFilter === 'rentType' }"
          @tap="openFilter('rentType')"
          v-if="showRentTypeFilter"
        >
          <text class="text-30rpx">{{ rentTypeFilterText }}</text>
          <text
            class="i-carbon-chevron-down text-24rpx ml-8rpx transition-all"
            :class="{ 'rotate-180': activeFilter === 'rentType' }"
          ></text>
        </view>

        <!-- 商铺类型筛选 -->
        <view
          class="filter-tab mr-30rpx"
          :class="{ active: activeFilter === 'spaceType' }"
          @tap="openFilter('spaceType')"
          v-if="showSpaceTypeFilter"
        >
          <text class="text-30rpx">{{ spaceTypeFilterText }}</text>
          <text
            class="i-carbon-chevron-down text-24rpx ml-8rpx transition-all"
            :class="{ 'rotate-180': activeFilter === 'spaceType' }"
          ></text>
        </view>

        <!-- 特色筛选 -->
        <view
          class="filter-tab mr-30rpx"
          :class="{ active: activeFilter === 'features' }"
          @tap="openFilter('features')"
          v-if="showFeaturesFilter"
        >
          <text class="text-30rpx">{{ featuresFilterText }}</text>
          <text
            class="i-carbon-chevron-down text-24rpx ml-8rpx transition-all"
            :class="{ 'rotate-180': activeFilter === 'features' }"
          ></text>
        </view>

        <!-- 排序筛选 -->
        <view
          class="filter-tab"
          :class="{ active: activeFilter === 'sort' }"
          @tap="openFilter('sort')"
        >
          <text class="text-30rpx">{{ sortFilterText }}</text>
          <text
            class="i-carbon-chevron-down text-24rpx ml-8rpx transition-all"
            :class="{ 'rotate-180': activeFilter === 'sort' }"
          ></text>
        </view>
      </view>
    </scroll-view>

    <!-- 区域筛选面板 -->
    <view class="filter-panel bg-white" v-if="activeFilter === 'area'">
      <scroll-view scroll-y class="panel-scroll">
        <view class="filter-options px-20rpx py-30rpx">
          <view
            v-for="(item, index) in areaOptions"
            :key="index"
            class="option-item py-20rpx"
            :class="{ active: filters.area === item.value }"
            @tap="selectFilter('area', item.value)"
          >
            <text>{{ item.label }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 价格筛选面板 -->
    <view class="filter-panel bg-white" v-if="activeFilter === 'price'">
      <scroll-view scroll-y class="panel-scroll">
        <view class="filter-options px-20rpx py-30rpx">
          <view
            v-for="(item, index) in priceOptions"
            :key="index"
            class="option-item py-20rpx"
            :class="{ active: filters.priceRange === item.value }"
            @tap="selectFilter('priceRange', item.value)"
          >
            <text>{{ item.label }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 户型筛选面板 -->
    <view class="filter-panel bg-white" v-if="activeFilter === 'houseType'">
      <scroll-view scroll-y class="panel-scroll">
        <view class="filter-options px-20rpx py-30rpx">
          <view
            v-for="(item, index) in houseTypeOptions"
            :key="index"
            class="option-item py-20rpx"
            :class="{ active: filters.houseType === item.value }"
            @tap="selectFilter('houseType', item.value)"
          >
            <text>{{ item.label }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 租房方式筛选面板 -->
    <view class="filter-panel bg-white" v-if="activeFilter === 'rentType'">
      <scroll-view scroll-y class="panel-scroll">
        <view class="filter-options px-20rpx py-30rpx">
          <view
            v-for="(item, index) in rentTypeOptions"
            :key="index"
            class="option-item py-20rpx"
            :class="{ active: filters.rentType === item.value }"
            @tap="selectFilter('rentType', item.value)"
          >
            <text>{{ item.label }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 商铺类型筛选面板 -->
    <view class="filter-panel bg-white" v-if="activeFilter === 'spaceType'">
      <scroll-view scroll-y class="panel-scroll">
        <view class="filter-options px-20rpx py-30rpx">
          <view
            v-for="(item, index) in spaceTypeOptions"
            :key="index"
            class="option-item py-20rpx"
            :class="{ active: filters.spaceType === item.value }"
            @tap="selectFilter('spaceType', item.value)"
          >
            <text>{{ item.label }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 特色筛选面板 -->
    <view class="filter-panel bg-white" v-if="activeFilter === 'features'">
      <scroll-view scroll-y class="panel-scroll">
        <view class="filter-options px-20rpx py-30rpx">
          <view class="feature-grid grid grid-cols-3 gap-20rpx">
            <view
              v-for="(item, index) in featuresOptions"
              :key="index"
              class="feature-item px-10rpx py-16rpx text-center rounded"
              :class="{ active: selectedFeatures.includes(item.value) }"
              @tap="toggleFeature(item.value)"
            >
              <text>{{ item.label }}</text>
            </view>
          </view>

          <view
            class="feature-actions flex justify-between mt-30rpx pt-20rpx border-top"
          >
            <view class="action-btn" @tap="resetFeatures">重置</view>
            <view class="action-btn confirm" @tap="confirmFeatures">确定</view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 排序方式筛选面板 -->
    <view class="filter-panel bg-white" v-if="activeFilter === 'sort'">
      <scroll-view scroll-y class="panel-scroll">
        <view class="filter-options px-20rpx py-30rpx">
          <view
            v-for="(item, index) in sortOptions"
            :key="index"
            class="option-item py-20rpx"
            :class="{ active: filters.sortBy === item.value }"
            @tap="selectFilter('sortBy', item.value)"
          >
            <text>{{ item.label }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 遮罩层 -->
    <view class="mask" v-if="activeFilter !== ''" @tap="closeFilter"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, reactive, defineProps, defineEmits, watch } from "vue";

// 定义属性
const props = defineProps({
  activeFilters: {
    type: Object,
    default: () => ({}),
  },
  filterType: {
    type: String,
    default: "rent", // rent, new, second, commercial
  },
});

// 定义事件
const emit = defineEmits(["filter-change"]);

// 当前激活的筛选条件
const activeFilter = ref("");

// 筛选条件
const filters = reactive({
  area: props.activeFilters.area || undefined,
  priceRange: props.activeFilters.priceRange || undefined,
  houseType: props.activeFilters.houseType || undefined,
  rentType: props.activeFilters.rentType || undefined,
  spaceType: props.activeFilters.spaceType || undefined,
  features: props.activeFilters.features || undefined,
  sortBy: props.activeFilters.sortBy || "默认排序",
});

// 监听activeFilters变化
watch(
  () => props.activeFilters,
  (newVal) => {
    if (newVal) {
      Object.assign(filters, newVal);
    }
  },
  { deep: true }
);

// 特色标签多选
const selectedFeatures = ref(
  props.activeFilters.features ? [...props.activeFilters.features] : []
);

// 显示控制
const showHouseTypeFilter = computed(() =>
  ["rent", "new", "second"].includes(props.filterType)
);
const showRentTypeFilter = computed(() => props.filterType === "rent");
const showSpaceTypeFilter = computed(() => props.filterType === "commercial");
const showFeaturesFilter = computed(() =>
  ["new", "second", "commercial"].includes(props.filterType)
);

// 区域筛选选项
const areaOptions = [
  { label: "不限", value: undefined },
  { label: "城中区", value: "城中区" },
  { label: "城东区", value: "城东区" },
  { label: "城南区", value: "城南区" },
  { label: "城西区", value: "城西区" },
  { label: "城北区", value: "城北区" },
  { label: "高新区", value: "高新区" },
  { label: "开发区", value: "开发区" },
];

// 价格筛选选项 - 根据不同类型房源显示不同价格区间
const priceOptions = computed(() => {
  switch (props.filterType) {
    case "new":
    case "second":
      return [
        { label: "不限", value: undefined },
        { label: "50万以下", value: "0-50" },
        { label: "50-80万", value: "50-80" },
        { label: "80-100万", value: "80-100" },
        { label: "100-150万", value: "100-150" },
        { label: "150-200万", value: "150-200" },
        { label: "200-300万", value: "200-300" },
        { label: "300万以上", value: "300-999999" },
      ];
    case "rent":
      return [
        { label: "不限", value: undefined },
        { label: "500元以下", value: "0-500" },
        { label: "500-1000元", value: "500-1000" },
        { label: "1000-1500元", value: "1000-1500" },
        { label: "1500-2000元", value: "1500-2000" },
        { label: "2000-3000元", value: "2000-3000" },
        { label: "3000-5000元", value: "3000-5000" },
        { label: "5000元以上", value: "5000-999999" },
      ];
    case "commercial":
      return [
        { label: "不限", value: undefined },
        { label: "1000元以下", value: "0-1000" },
        { label: "1000-3000元", value: "1000-3000" },
        { label: "3000-5000元", value: "3000-5000" },
        { label: "5000-10000元", value: "5000-10000" },
        { label: "10000元以上", value: "10000-999999" },
      ];
    default:
      return [
        { label: "不限", value: undefined },
        { label: "价格低-高", value: "price_asc" },
        { label: "价格高-低", value: "price_desc" },
      ];
  }
});

// 户型筛选选项
const houseTypeOptions = [
  { label: "不限", value: undefined },
  { label: "1室", value: "1室" },
  { label: "2室", value: "2室" },
  { label: "3室", value: "3室" },
  { label: "4室", value: "4室" },
  { label: "5室及以上", value: "5室及以上" },
];

// 租房方式筛选选项
const rentTypeOptions = [
  { label: "不限", value: undefined },
  { label: "整租", value: "整租" },
  { label: "合租", value: "合租" },
  { label: "短租", value: "短租" },
];

// 商铺类型筛选选项
const spaceTypeOptions = [
  { label: "不限", value: undefined },
  { label: "商铺", value: "商铺" },
  { label: "写字楼", value: "写字楼" },
  { label: "厂房", value: "厂房" },
  { label: "仓库", value: "仓库" },
];

// 特色筛选选项 - 根据不同类型房源显示不同特色标签
const featuresOptions = computed(() => {
  switch (props.filterType) {
    case "new":
      return [
        { label: "近地铁", value: "近地铁" },
        { label: "学区房", value: "学区房" },
        { label: "公园旁", value: "公园旁" },
        { label: "精装修", value: "精装修" },
        { label: "低总价", value: "低总价" },
        { label: "小户型", value: "小户型" },
        { label: "大户型", value: "大户型" },
        { label: "南北通透", value: "南北通透" },
        { label: "品牌开发商", value: "品牌开发商" },
      ];
    case "second":
      return [
        { label: "近地铁", value: "近地铁" },
        { label: "学区房", value: "学区房" },
        { label: "满二年", value: "满二年" },
        { label: "满五年", value: "满五年" },
        { label: "唯一住房", value: "唯一住房" },
        { label: "精装修", value: "精装修" },
        { label: "南北通透", value: "南北通透" },
        { label: "电梯房", value: "电梯房" },
        { label: "降价房", value: "降价房" },
      ];
    case "commercial":
      return [
        { label: "临街", value: "临街" },
        { label: "交通便利", value: "交通便利" },
        { label: "人流量大", value: "人流量大" },
        { label: "单价低", value: "单价低" },
        { label: "可明火", value: "可明火" },
        { label: "带租约", value: "带租约" },
        { label: "可分割", value: "可分割" },
        { label: "独栋", value: "独栋" },
        { label: "性价比高", value: "性价比高" },
      ];
    default:
      return [];
  }
});

// 排序方式筛选选项
const sortOptions = [
  { label: "默认排序", value: "默认排序" },
  { label: "价格从低到高", value: "价格从低到高" },
  { label: "价格从高到低", value: "价格从高到低" },
  { label: "面积从大到小", value: "面积从大到小" },
  { label: "面积从小到大", value: "面积从小到大" },
  { label: "最新发布", value: "最新发布" },
];

// 筛选条件显示文本
const areaFilterText = computed(() => filters.area || "区域");
const priceFilterText = computed(() => {
  if (!filters.priceRange) return "价格";

  // 根据筛选值获取对应的标签文本
  const option = priceOptions.value.find(
    (item) => item.value === filters.priceRange
  );
  return option ? option.label : "价格";
});
const houseTypeFilterText = computed(() => {
  if (!filters.houseType) return "户型";
  return filters.houseType;
});
const rentTypeFilterText = computed(() => {
  if (!filters.rentType) return "租房方式";
  return filters.rentType;
});
const spaceTypeFilterText = computed(() => {
  if (!filters.spaceType) return "类型";
  return filters.spaceType;
});
const featuresFilterText = computed(() => {
  if (!filters.features || filters.features.length === 0) return "特色";
  return `已选${filters.features.length}个`;
});
const sortFilterText = computed(() => filters.sortBy || "排序");

// 打开筛选面板
const openFilter = (filter: string) => {
  if (activeFilter.value === filter) {
    activeFilter.value = "";
  } else {
    activeFilter.value = filter;
  }
};

// 关闭筛选面板
const closeFilter = () => {
  activeFilter.value = "";
};

// 选择筛选条件
const selectFilter = (type: string, value: any) => {
  filters[type] = value;
  closeFilter();
  emitFilterChange();
};

// 切换特色标签
const toggleFeature = (value: string) => {
  const index = selectedFeatures.value.indexOf(value);
  if (index > -1) {
    selectedFeatures.value.splice(index, 1);
  } else {
    selectedFeatures.value.push(value);
  }
};

// 重置特色标签
const resetFeatures = () => {
  selectedFeatures.value = [];
};

// 确认特色标签选择
const confirmFeatures = () => {
  filters.features =
    selectedFeatures.value.length > 0 ? [...selectedFeatures.value] : undefined;
  closeFilter();
  emitFilterChange();
};

// 发送筛选条件变化事件
const emitFilterChange = () => {
  emit("filter-change", { ...filters });
};
</script>

<style lang="scss" scoped>
.house-filter {
  position: relative;
  z-index: 100;
  border-bottom: 1px solid #f0f0f0;
}

.filter-scroll-view {
  white-space: nowrap;
}

.filter-tabs {
  height: 88rpx;
  align-items: center;
}

.filter-tab {
  display: flex;
  align-items: center;
  height: 88rpx;
  color: #666;

  &.active {
    color: $primary;
  }
}

.filter-panel {
  position: absolute;
  top: 88rpx;
  left: 0;
  width: 100%;
  max-height: 600rpx;
  z-index: 120;
  border-bottom: 1px solid #f0f0f0;
  animation: slideDown 0.2s ease-out;
}

.panel-scroll {
  max-height: 600rpx;
}

.option-item {
  font-size: 28rpx;
  color: #333;

  &.active {
    color: $primary;
    font-weight: 500;
  }
}

.feature-grid {
  margin-bottom: 30rpx;
}

.feature-item {
  font-size: 26rpx;
  background-color: #f8f8f8;

  &.active {
    background-color: rgba($primary, 0.1);
    color: $primary;
  }
}

.feature-actions {
  .action-btn {
    padding: 15rpx 40rpx;
    font-size: 28rpx;
    border-radius: 6rpx;

    &.confirm {
      background-color: $primary;
      color: white;
    }
  }
}

.border-top {
  border-top: 1px solid #f0f0f0;
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 110;
}

@keyframes slideDown {
  from {
    transform: translateY(-20rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
