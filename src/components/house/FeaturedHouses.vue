<template>
  <view class="featured-houses bg-white px-20rpx py-30rpx mb-20rpx">
    <view class="section-header flex justify-between items-center mb-20rpx">
      <view class="section-title flex items-center">
        <text
          class="i-carbon-star-filled text-32rpx color-primary mr-10rpx"
        ></text>
        <text class="text-32rpx font-bold">特色房源</text>
      </view>
      <view class="more-link flex items-center">
        <text class="text-26rpx color-info">更多</text>
        <text class="i-carbon-chevron-right color-info text-24rpx"></text>
      </view>
    </view>

    <scroll-view scroll-x class="featured-scroll" show-scrollbar="false">
      <view class="featured-list flex">
        <view
          v-for="(house, index) in houses"
          :key="house.id"
          class="featured-item"
          :class="{ 'mr-20rpx': index < houses.length - 1 }"
          @tap="onItemClick(house.id)"
        >
          <view class="featured-card rounded-lg overflow-hidden">
            <!-- 图片区域 -->
            <image
              :src="house.images[0]"
              mode="aspectFill"
              class="featured-image"
            />

            <!-- 标签区域 -->
            <view class="tag-container absolute">
              <view v-if="house.isVip" class="tag vip-tag">
                <text class="i-carbon-star-filled mr-2rpx"></text>
                <text>自营</text>
              </view>
              <view v-if="house.isNew" class="tag new-tag ml-10rpx">新上</view>
            </view>

            <!-- 信息区域 -->
            <view class="info-container p-16rpx">
              <text class="title text-28rpx font-bold line-clamp-1">{{
                house.title
              }}</text>
              <view class="location flex items-center mt-8rpx">
                <text class="i-carbon-location color-grey text-22rpx"></text>
                <text
                  class="location-text color-grey text-24rpx line-clamp-1 ml-4rpx"
                  >{{ house.location }}</text
                >
              </view>
              <view class="features flex items-center justify-between mt-8rpx">
                <text class="tag-text">{{ house.houseType }}</text>
                <text class="tag-text">{{ house.area }}㎡</text>
                <view class="price-text color-primary">
                  <text class="text-30rpx font-bold">{{ house.price }}</text>
                  <text class="text-22rpx">元/月</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from "vue";
import type { HouseItemType } from "@/types/house";

const props = defineProps<{
  houses: HouseItemType[];
}>();

const emit = defineEmits(["item-click"]);

const onItemClick = (id: string) => {
  emit("item-click", id);
  uni.navigateTo({
    url: `/pages/house/detail?id=${id}`,
  });
};
</script>

<style lang="scss" scoped>
.section-title {
  font-size: 32rpx;
  font-weight: bold;
}

.color-primary {
  color: $primary;
}

.color-info {
  color: #666;
}

.color-grey {
  color: #999;
}

.featured-scroll {
  white-space: nowrap;
  padding: 10rpx 0;
}

.featured-list {
  display: inline-flex;
}

.featured-item {
  width: 320rpx;
  flex-shrink: 0;
}

.featured-card {
  background: white;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.featured-image {
  width: 100%;
  height: 200rpx;
}

.tag-container {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  display: flex;
}

.tag {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
}

.vip-tag {
  background-color: $primary;
  color: white;
}

.new-tag {
  background-color: #f74f55;
  color: white;
}

.tag-text {
  font-size: 24rpx;
  color: #666;
}
</style>
