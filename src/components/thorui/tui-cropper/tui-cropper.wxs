var cropper = {
	cutX: 0, //画布x轴起点
	cutY: 0, //画布y轴起点0
	touchRelative: [{
		x: 0,
		y: 0
	}], //手指或鼠标和图片中心的相对位置
	hypotenuseLength: 0, //双指触摸时斜边长度
	flagEndTouch: false, //是否结束触摸
	canvasWidth: 0,
	canvasHeight: 0,
	imgWidth: 0, //图片宽度
	imgHeight: 0, //图片高度
	scale: 1, //图片缩放比
	angle: 0, //图片旋转角度
	imgTop: 0, //图片上边距
	imgLeft: 0, //图片左边距
	windowHeight: 0,
	windowWidth: 0,
	init: true
}

function bool(str) {
	return str === 'true' || str == true ? true : false
}

function touchstart(e, ins) {
	var touch = e.touches || e.changedTouches;
	cropper.flagEndTouch = false;
	if (touch.length == 1) {
		cropper.touchRelative[0] = {
			x: touch[0].pageX - cropper.imgLeft,
			y: touch[0].pageY - cropper.imgTop
		};
	} else {
		var width = Math.abs(touch[0].pageX - touch[1].pageX);
		var height = Math.abs(touch[0].pageY - touch[1].pageY);
		cropper.touchRelative = [{
				x: touch[0].pageX - cropper.imgLeft,
				y: touch[0].pageY - cropper.imgTop
			},
			{
				x: touch[1].pageX - cropper.imgLeft,
				y: touch[1].pageY - cropper.imgTop
			}
		];
		cropper.hypotenuseLength = Math.sqrt(Math.pow(width, 2) + Math.pow(height, 2));
	}

}

function moveDuring(ins) {
	if (!ins) return;
	ins.callMethod('moveDuring')
}

function moveStop(ins) {
	if (!ins) return;
	ins.callMethod('moveStop')
};

function setCutCenter(ins) {
	var cutY = (cropper.windowHeight - cropper.canvasHeight) * 0.5;
	var cutX = (cropper.windowWidth - cropper.canvasWidth) * 0.5;
	//顺序不能变
	cropper.imgTop = cropper.imgTop - cropper.cutY + cutY;
	cropper.cutY = cutY; //截取的框上边距
	cropper.imgLeft = cropper.imgLeft - cropper.cutX + cutX;
	cropper.cutX = cutX; //截取的框左边距
	cutDetectionPosition(ins)
	imgTransform(ins)
	updateData(ins)
}

function touchmove(e, ins) {
	var touch = e.touches || e.changedTouches;
	if (cropper.flagEndTouch) return;
	moveDuring(ins);
	if (e.touches.length == 1) {
		var left = touch[0].pageX - cropper.touchRelative[0].x,
			top = touch[0].pageY - cropper.touchRelative[0].y;
		cropper.imgLeft = left;
		cropper.imgTop = top;
		imgTransform(ins);
		imgMarginDetectionPosition(ins);
	} else {
		var res = e.instance.getDataset();
		var minScale = +res.minscale;
		var maxScale = +res.maxscale;
		var width = Math.abs(touch[0].pageX - touch[1].pageX),
			height = Math.abs(touch[0].pageY - touch[1].pageY),
			hypotenuse = Math.sqrt(Math.pow(width, 2) + Math.pow(height, 2)),
			scale = cropper.scale * (hypotenuse / cropper.hypotenuseLength),
			current_deg = 0;
		scale = scale <= minScale ? minScale : scale;
		scale = scale >= maxScale ? maxScale : scale;
		cropper.scale = scale;
		imgMarginDetectionScale(ins, true);
		var touchRelative = [{
				x: touch[0].pageX - cropper.imgLeft,
				y: touch[0].pageY - cropper.imgTop
			},
			{
				x: touch[1].pageX - cropper.imgLeft,
				y: touch[1].pageY - cropper.imgTop
			}
		];
		cropper.touchRelative = touchRelative;
		cropper.hypotenuseLength = Math.sqrt(Math.pow(width, 2) + Math.pow(height, 2));
		//更新视图
		cropper.angle = cropper.angle + current_deg;
		imgTransform(ins);
	}
}

function touchend(e, ins) {
	cropper.flagEndTouch = true;
	moveStop(ins);
	updateData(ins)
}


//检测剪裁框位置是否在允许的范围内(屏幕内)
function cutDetectionPosition(ins) {
	var windowHeight = cropper.windowHeight,
		windowWidth = cropper.windowWidth;

	var cutDetectionPositionTop = function() {
		//检测上边距是否在范围内
		if (cropper.cutY < 0) {
			cropper.cutY = 0;
		}
		if (cropper.cutY > windowHeight - cropper.canvasHeight) {
			cropper.cutY = windowHeight - cropper.canvasHeight;
		}
	}

	var cutDetectionPositionLeft = function() {
		//检测左边距是否在范围内
		if (cropper.cutX < 0) {
			cropper.cutX = 0;
		}
		if (cropper.cutX > windowWidth - cropper.canvasWidth) {
			cropper.cutX = windowWidth - cropper.canvasWidth;
		}
	}
	//裁剪框坐标处理（如果只写一个参数则另一个默认为0，都不写默认居中）
	if (cropper.cutY == null && cropper.cutX == null) {
		var cutY = (windowHeight - cropper.canvasHeight) * 0.5;
		var cutX = (windowWidth - cropper.canvasWidth) * 0.5;
		cropper.cutY = cutY; //截取的框上边距
		cropper.cutX = cutX; //截取的框左边距
	} else if (cropper.cutY != null && cropper.cutX != null) {
		cutDetectionPositionTop();
		cutDetectionPositionLeft();
	} else if (cropper.cutY != null && cropper.cutX == null) {
		cutDetectionPositionTop();
		cropper.cutX = (windowWidth - cropper.canvasWidth) / 2;
	} else if (cropper.cutY == null && cropper.cutX != null) {
		cutDetectionPositionLeft();
		cropper.cutY = (windowHeight - cropper.canvasHeight) / 2;
	}
}

/**
 * 图片边缘检测-缩放
 */
function imgMarginDetectionScale(ins, delay) {
	var scale = cropper.scale;
	var imgWidth = cropper.imgWidth;
	var imgHeight = cropper.imgHeight;
	if ((cropper.angle / 90) % 2) {
		imgWidth = cropper.imgHeight;
		imgHeight = cropper.imgWidth;
	}
	if (imgWidth * scale < cropper.canvasWidth) {
		scale = cropper.canvasWidth / imgWidth;
	}
	if (imgHeight * scale < cropper.canvasHeight) {
		scale = Math.max(scale, cropper.canvasHeight / imgHeight);
	}
	imgMarginDetectionPosition(ins, scale, delay);
}
/**
 * 图片边缘检测-位置
 */
function imgMarginDetectionPosition(ins, scale, delay) {
	var left = cropper.imgLeft;
	var top = cropper.imgTop;
	scale = scale || cropper.scale;
	var imgWidth = cropper.imgWidth;
	var imgHeight = cropper.imgHeight;
	if ((cropper.angle / 90) % 2) {
		imgWidth = cropper.imgHeight;
		imgHeight = cropper.imgWidth;
	}
	left = cropper.cutX + (imgWidth * scale) / 2 >= left ? left : cropper.cutX + (imgWidth * scale) / 2;
	left = cropper.cutX + cropper.canvasWidth - (imgWidth * scale) / 2 <= left ? left : cropper.cutX + cropper.canvasWidth -
		(imgWidth * scale) / 2;
	top = cropper.cutY + (imgHeight * scale) / 2 >= top ? top : cropper.cutY + (imgHeight * scale) / 2;
	top = cropper.cutY + cropper.canvasHeight - (imgHeight * scale) / 2 <= top ? top : cropper.cutY + cropper.canvasHeight -
		(imgHeight * scale) / 2;

	cropper.imgLeft = left;
	cropper.imgTop = top;
	cropper.scale = scale;
	if (!delay || delay === 'null') {
		imgTransform(ins);
	}
}


//改变截取框大小
function computeCutSize(ins) {
	if (cropper.canvasWidth > cropper.windowWidth) {
		cropper.canvasWidth = cropper.windowWidth;
	} else if (cropper.canvasWidth + cropper.cutX > cropper.windowWidth) {
		cropper.cutX = cropper.windowWidth - cropper.cutX;
	}
	if (cropper.canvasHeight > cropper.windowHeight) {
		cropper.canvasHeight = cropper.windowHeight;
	} else if (cropper.canvasHeight + cropper.cutY > cropper.windowHeight) {
		cropper.cutY = cropper.windowHeight - cropper.cutY;
	}
}

function imgTransform(ins) {
	var owner = ins.selectComponent('.tui-cropper__image')
	if (!owner) return
	var x = cropper.imgLeft - cropper.imgWidth / 2;
	var y = cropper.imgTop - cropper.imgHeight / 2;
	owner.setStyle({
		'transform': 'translate3d(' + x + 'px,' + y + 'px,0) scale(' + cropper.scale + ') rotate(' + cropper.angle + 'deg)'
	})
}

function imageReset(ins) {
	cropper.scale = 1;
	cropper.angle = 0;
	imgTransform(ins);
}
//监听截取框宽高变化
function canvasWidth(ins) {
	if (!ins) return;
	computeCutSize(ins);
}

function canvasHeight(ins) {
	if (!ins) return;
	computeCutSize(ins);
}

function updateData(ins) {
	if (!ins) return;
	ins.callMethod('change', {
		cutX: cropper.cutX,
		cutY: cropper.cutY,
		imgWidth: cropper.imgWidth,
		imgHeight: cropper.imgHeight,
		scale: cropper.scale,
		angle: cropper.angle,
		imgTop: cropper.imgTop,
		imgLeft: cropper.imgLeft
	})
}

function propsChange(prop, oldProp, ownerInstance, ins) {
	if (prop && prop !== 'null') {
		var params = prop.split(',')
		var type = +params[0]
		var dataset = ins.getDataset();
		if (cropper.init || type == 4) {
			cropper.canvasWidth = +dataset.width;
			cropper.canvasHeight = +dataset.height;
			cropper.imgTop = dataset.windowheight / 2;
			cropper.imgLeft = dataset.windowwidth / 2;
			cropper.imgWidth = +dataset.imgwidth;
			cropper.imgHeight = +dataset.imgheight;
			cropper.windowHeight = +dataset.windowheight;
			cropper.windowWidth = +dataset.windowwidth;
			cropper.init = false
		} else if (type == 2 || type == 3) {
			cropper.imgWidth = +dataset.imgwidth;
			cropper.imgHeight = +dataset.imgheight;
		}
		cropper.angle = +dataset.angle;
		if (type == 3) {
			imgTransform(ownerInstance);
		}
		switch (type) {
			case 1:
				setCutCenter(ownerInstance);
				//设置裁剪框大小>设置图片尺寸>绘制canvas
				computeCutSize(ownerInstance);
				//检查裁剪框是否在范围内
				cutDetectionPosition(ownerInstance);
				break;
			case 2:
				setCutCenter(ownerInstance);
				break;
			case 3:
				imgMarginDetectionScale(ownerInstance)
				break;
			case 4:
				imageReset(ownerInstance);
				break;
			case 5:
				setCutCenter(ownerInstance);
				break;
			default:
				break;
		}
	}
}

module.exports = {
	touchstart: touchstart,
	touchmove: touchmove,
	touchend: touchend,
	propsChange: propsChange
}
