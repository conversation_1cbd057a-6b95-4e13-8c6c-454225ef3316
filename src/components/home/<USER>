<template>
  <view class="house-container pb-100rpx">
    <!-- 广告轮播图 -->
    <view v-if="showBanner" class="ad-banner-container px-20rpx">
      <swiper
        class="ad-banner-swiper"
        :indicator-dots="true"
        :autoplay="true"
        :interval="3000"
        :duration="500"
        indicator-color="rgba(255, 255, 255, 0.6)"
        indicator-active-color="$primary"
      >
        <swiper-item v-for="(item, index) in adBannerList" :key="index">
          <image :src="item.image" mode="aspectFill" class="ad-banner-image" />
        </swiper-item>
      </swiper>
    </view>

    <!-- 进入找房首页入口 -->
    <view class="enter-house-index bg-white px-30rpx py-20rpx mb-20rpx">
      <view
        class="flex justify-between items-center"
        @tap="navigateToHouseIndex"
      >
        <view class="flex items-center">
          <text class="i-carbon-home text-40rpx color-primary mr-10rpx"></text>
          <text class="text-32rpx font-bold">找房首页</text>
        </view>
        <view class="flex items-center">
          <text class="text-26rpx color-grey">进入更丰富的找房体验</text>
          <text
            class="i-carbon-chevron-right text-24rpx color-grey ml-6rpx"
          ></text>
        </view>
      </view>
    </view>

    <!-- 房源类型选择器 -->
    <view class="house-type-tabs bg-white px-20rpx py-20rpx mb-20rpx">
      <scroll-view scroll-x class="scroll-view-x">
        <view class="house-type-list flex">
          <view
            v-for="(item, index) in houseTypeOptions"
            :key="index"
            class="house-type-item mr-30rpx px-20rpx py-10rpx rounded-full"
            :class="
              currentHouseType === item.id
                ? 'bg-primary text-white'
                : 'bg-gray-100'
            "
            @tap="changeHouseType(item.id)"
          >
            <text>{{ item.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 快捷入口 -->
    <view class="quick-nav bg-white px-20rpx py-30rpx mb-20rpx">
      <view class="grid grid-cols-5 gap-15rpx">
        <view
          v-for="(item, index) in currentTypeQuickNavs"
          :key="index"
          class="quick-nav-item flex flex-col items-center"
          @tap="navigateTo(item.path)"
        >
          <view class="nav-icon-circle mb-10rpx" :class="item.bgColor">
            <text :class="item.icon"></text>
          </view>
          <text class="text-24rpx color-info">{{ item.name }}</text>
        </view>
      </view>
    </view>

    <!-- 商家发布入口 -->
    <view
      class="publisher-entry bg-white px-30rpx py-20rpx mb-20rpx flex items-center justify-between"
    >
      <view class="flex items-center">
        <text class="i-carbon-store text-42rpx color-primary mr-10rpx"></text>
        <view class="ml-10rpx">
          <text class="text-30rpx font-bold">我是房产经纪人/业主</text>
          <text class="text-24rpx color-grey block">免费发布房源信息</text>
        </view>
      </view>
      <button
        class="publish-btn bg-primary text-white text-28rpx px-30rpx py-14rpx rounded-full"
        @tap="navigateToPublish"
      >
        发布房源
      </button>
    </view>

    <!-- 使用HouseFilter组件 -->
    <view class="mb-20rpx">
      <house-filter
        :activeFilters="activeFilters"
        @filter-change="handleFilterChange"
      ></house-filter>
    </view>

    <!-- 根据当前选择的房源类型展示相应内容 -->
    <template v-if="currentHouseType === 'new'">
      <new-house-content></new-house-content>
    </template>

    <template v-else-if="currentHouseType === 'second'">
      <second-house-content></second-house-content>
    </template>

    <template v-else-if="currentHouseType === 'rent'">
      <!-- 使用FeaturedHouses组件 -->
      <view class="mb-20rpx" v-if="featuredHouses.length > 0">
        <featured-houses
          :houses="featuredHouses"
          @item-click="navigateToDetail"
        ></featured-houses>
      </view>

      <!-- 推荐房源 -->
      <view class="recommend-houses bg-white mb-20rpx">
        <view
          class="section-header flex justify-between items-center px-20rpx py-20rpx"
        >
          <text class="text-32rpx font-bold">推荐租房</text>
          <view class="flex items-center" @tap="navigateToList('rent')">
            <text class="text-26rpx color-grey">更多好房</text>
            <text class="i-carbon-chevron-right color-grey"></text>
          </view>
        </view>

        <!-- 使用HouseItem组件 -->
        <view class="house-list px-20rpx pb-20rpx">
          <house-item
            v-for="house in recommendHouses"
            :key="house.id"
            :house="house"
            class="mb-20rpx"
            @tap="navigateToDetail(house.id)"
          ></house-item>
        </view>
      </view>
    </template>

    <template v-else-if="currentHouseType === 'commercial'">
      <commercial-house-content></commercial-house-content>
    </template>

    <!-- 最新房源动态 -->
    <view class="latest-dynamic bg-white mb-20rpx">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">最新房源动态</text>
        <view class="flex items-center" @tap="navigateToList('dynamic')">
          <text class="text-26rpx color-grey">更多动态</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>

      <view class="dynamic-list px-20rpx pb-20rpx">
        <view
          v-for="(item, index) in houseDynamics"
          :key="index"
          class="dynamic-item py-20rpx"
          :class="{ 'border-bottom': index < houseDynamics.length - 1 }"
        >
          <view class="flex items-center">
            <image
              :src="item.avatar"
              class="avatar rounded-full"
              mode="aspectFill"
            ></image>
            <view class="ml-20rpx">
              <view class="flex items-center">
                <text class="text-28rpx font-bold">{{ item.name }}</text>
                <text v-if="item.verified" class="verified-badge ml-10rpx"
                  >已认证</text
                >
              </view>
              <text class="text-24rpx color-grey">{{ item.time }}</text>
            </view>
          </view>
          <view class="dynamic-content mt-20rpx">
            <text class="text-28rpx">{{ item.content }}</text>
            <view class="house-tag-list flex flex-wrap mt-10rpx">
              <text
                v-for="(tag, tIndex) in item.tags"
                :key="tIndex"
                class="house-tag mr-10rpx mb-10rpx"
                >{{ tag }}</text
              >
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 租房攻略 -->
    <view class="rent-guides bg-white">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">房产知识</text>
        <view class="flex items-center" @tap="navigateToGuides">
          <text class="text-26rpx color-grey">查看全部</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>

      <view class="guides-list px-20rpx pb-20rpx">
        <view
          v-for="(guide, index) in rentGuides"
          :key="index"
          class="guide-item flex p-20rpx mb-20rpx bg-white rounded-lg shadow-sm"
          @tap="navigateToGuideDetail(guide.id)"
        >
          <image
            :src="guide.image"
            mode="aspectFill"
            class="guide-image rounded-lg"
          />
          <view class="guide-info flex-1 ml-20rpx">
            <text class="guide-title text-30rpx font-bold line-clamp-2">{{
              guide.title
            }}</text>
            <text
              class="guide-desc text-26rpx color-grey line-clamp-2 mt-10rpx"
              >{{ guide.description }}</text
            >
            <view class="flex justify-between items-center mt-10rpx">
              <text class="author text-24rpx color-grey">{{
                guide.author
              }}</text>
              <text class="view-count text-24rpx color-grey"
                >{{ guide.viewCount }}阅读</text
              >
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { defineProps, ref, reactive, computed } from "vue";
import HouseItem from "@/components/house/HouseItem.vue";
import FeaturedHouses from "@/components/house/FeaturedHouses.vue";
import HouseFilter from "@/components/house/HouseFilter.vue";
import { generateMoreHouses } from "@/utils/data/houseListData";
import type { HouseItemType, HouseFilterType } from "@/types/house";

// 定义组件，需要导入子组件（这些需要开发）
// import NewHouseContent from "@/components/house/NewHouseContent.vue";
// import SecondHouseContent from "@/components/house/SecondHouseContent.vue";
// import CommercialHouseContent from "@/components/house/CommercialHouseContent.vue";

// 定义props接收是否显示广告banner
const props = defineProps({
  showBanner: {
    type: Boolean,
    default: true,
  },
});

// 当前选中的房源类型
const currentHouseType = ref("rent"); // 默认为租房类型

// 房源类型选项
const houseTypeOptions = [
  { id: "new", name: "新房" },
  { id: "second", name: "二手房" },
  { id: "rent", name: "租房" },
  { id: "commercial", name: "商铺办公" },
];

// 切换房源类型
const changeHouseType = (typeId: string) => {
  currentHouseType.value = typeId;
  // 根据类型更新筛选条件
  resetFilters(typeId);
};

// 根据房源类型重置筛选条件
const resetFilters = (typeId: string) => {
  // 根据不同类型设置不同的默认筛选条件
  switch (typeId) {
    case "new":
      Object.assign(activeFilters, {
        area: undefined,
        priceRange: undefined,
        houseType: undefined,
        sortBy: "默认排序",
        features: undefined,
      });
      break;
    case "second":
      Object.assign(activeFilters, {
        area: undefined,
        priceRange: undefined,
        houseType: undefined,
        sortBy: "默认排序",
        features: undefined,
      });
      break;
    case "rent":
      Object.assign(activeFilters, {
        area: undefined,
        rentType: undefined,
        priceRange: undefined,
        houseType: undefined,
        sortBy: "默认排序",
      });
      break;
    case "commercial":
      Object.assign(activeFilters, {
        area: undefined,
        priceRange: undefined,
        spaceType: undefined,
        sortBy: "默认排序",
        features: undefined,
      });
      break;
  }
};

// 广告轮播图数据 - 使用Picsum Photos免费图片
const adBannerList = [
  {
    image: "https://picsum.photos/seed/house1/800/300",
    link: "https://example.com/house/ad1",
  },
  {
    image: "https://picsum.photos/seed/house2/800/300",
    link: "https://example.com/house/ad2",
  },
  {
    image: "https://picsum.photos/seed/house3/800/300",
    link: "https://example.com/house/ad3",
  },
];

// 根据当前房源类型获取快捷导航
const currentTypeQuickNavs = computed(() => {
  switch (currentHouseType.value) {
    case "new":
      return [
        {
          name: "热门楼盘",
          icon: "i-carbon-building",
          bgColor: "bg-red-500",
          path: "/pages/house/newHouse/list?filter=hot",
        },
        {
          name: "本月开盘",
          icon: "i-carbon-calendar",
          bgColor: "bg-blue-500",
          path: "/pages/house/newHouse/list?filter=month",
        },
        {
          name: "低总价",
          icon: "i-carbon-money",
          bgColor: "bg-green-500",
          path: "/pages/house/newHouse/list?filter=lowprice",
        },
        {
          name: "小户型",
          icon: "i-carbon-workspace",
          bgColor: "bg-yellow-500",
          path: "/pages/house/newHouse/list?filter=small",
        },
        {
          name: "地图找房",
          icon: "i-carbon-map",
          bgColor: "bg-orange-500",
          path: "/pages/house/map?type=new",
        },
      ];
    case "second":
      return [
        {
          name: "降价房",
          icon: "i-carbon-cut",
          bgColor: "bg-red-500",
          path: "/pages/house/secondHouse/list?filter=price_cut",
        },
        {
          name: "近地铁",
          icon: "i-carbon-train",
          bgColor: "bg-blue-500",
          path: "/pages/house/secondHouse/list?filter=metro",
        },
        {
          name: "学区房",
          icon: "i-carbon-education",
          bgColor: "bg-green-500",
          path: "/pages/house/secondHouse/list?filter=school",
        },
        {
          name: "满二年",
          icon: "i-carbon-certificate",
          bgColor: "bg-yellow-500",
          path: "/pages/house/secondHouse/list?filter=overTwoYears",
        },
        {
          name: "地图找房",
          icon: "i-carbon-map",
          bgColor: "bg-orange-500",
          path: "/pages/house/map?type=second",
        },
      ];
    case "rent":
      return [
        {
          name: "整租",
          icon: "i-carbon-home",
          bgColor: "bg-blue-500",
          path: "/pages/house/rent/list?rentType=整租",
        },
        {
          name: "合租",
          icon: "i-carbon-users",
          bgColor: "bg-green-500",
          path: "/pages/house/rent/list?rentType=合租",
        },
        {
          name: "品牌公寓",
          icon: "i-carbon-building",
          bgColor: "bg-purple-500",
          path: "/pages/house/rent/list?type=公寓",
        },
        {
          name: "短租",
          icon: "i-carbon-calendar",
          bgColor: "bg-pink-500",
          path: "/pages/house/rent/list?rentType=短租",
        },
        {
          name: "地图找房",
          icon: "i-carbon-map",
          bgColor: "bg-orange-500",
          path: "/pages/house/map?type=rent",
        },
      ];
    case "commercial":
      return [
        {
          name: "商铺",
          icon: "i-carbon-store",
          bgColor: "bg-red-500",
          path: "/pages/house/commercial/list?type=商铺",
        },
        {
          name: "写字楼",
          icon: "i-carbon-building",
          bgColor: "bg-blue-500",
          path: "/pages/house/commercial/list?type=写字楼",
        },
        {
          name: "厂房",
          icon: "i-carbon-factory",
          bgColor: "bg-green-500",
          path: "/pages/house/commercial/list?type=厂房",
        },
        {
          name: "仓库",
          icon: "i-carbon-box",
          bgColor: "bg-yellow-500",
          path: "/pages/house/commercial/list?type=仓库",
        },
        {
          name: "地图找房",
          icon: "i-carbon-map",
          bgColor: "bg-orange-500",
          path: "/pages/house/map?type=commercial",
        },
      ];
    default:
      return [];
  }
});

// 筛选条件
const activeFilters = reactive<HouseFilterType>({
  area: undefined,
  rentType: undefined,
  priceRange: undefined,
  houseType: undefined,
  sortBy: "默认排序",
});

// 推荐房源 - 使用generateMoreHouses来创建房源数据
const recommendHouses = ref<HouseItemType[]>(generateMoreHouses(3));

// 精选房源
const featuredHouses = ref<HouseItemType[]>(generateMoreHouses(3));

// 房源动态数据
const houseDynamics = [
  {
    id: "1",
    name: "张经理",
    avatar: "https://picsum.photos/seed/user1/100/100",
    verified: true,
    time: "今天 10:32",
    content:
      "刚刚上架一套学区房，中心小学和实验中学对口，三房两厅，南北通透，精装修，拎包入住！",
    tags: ["学区房", "精装修", "南北通透", "三室两厅"],
  },
  {
    id: "2",
    name: "李店长",
    avatar: "https://picsum.photos/seed/user2/100/100",
    verified: true,
    time: "昨天 15:45",
    content: "市中心黄金商铺出租，人流量大，适合各类零售、餐饮业态，月租金面议",
    tags: ["商铺", "市中心", "人流量大", "临街"],
  },
];

// 租房攻略 - 使用Picsum Photos免费图片
const rentGuides = [
  {
    id: "1",
    image: "https://picsum.photos/seed/guide1/400/300",
    title: "新手租房必看：小城市租房全攻略",
    description:
      "从找房看房到签约入住，详细讲解租房过程中需要注意的各种细节和陷阱...",
    author: "租房专家",
    viewCount: "25.8万",
  },
  {
    id: "2",
    image: "https://picsum.photos/seed/guide2/400/300",
    title: "房屋租赁合同怎么签？这些条款必须注意",
    description:
      "详细解读房屋租赁合同中的各项条款，教你如何避免合同陷阱保障自身权益...",
    author: "法律顾问",
    viewCount: "18.3万",
  },
  {
    id: "3",
    image: "https://picsum.photos/seed/guide3/400/300",
    title: "三四线城市楼市分析：买房还是租房更划算？",
    description:
      "针对当前三四线城市的房价和租金情况，分析买房和租房的性价比，让你理性决策...",
    author: "房产分析师",
    viewCount: "12.6万",
  },
];

// 处理筛选条件变化
const handleFilterChange = (filter: HouseFilterType) => {
  Object.assign(activeFilters, filter);
  // 这里可以根据筛选条件刷新列表数据
};

// 导航到详情页
const navigateToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/${currentHouseType.value}/detail?id=${id}`,
  });
};

// 导航到列表页
const navigateToList = (type: string = "") => {
  const url =
    type === "dynamic"
      ? "/pages/house/dynamic/list"
      : `/pages/house/${currentHouseType.value}/list`;

  uni.navigateTo({
    url,
  });
};

// 导航到找房首页
const navigateToHouseIndex = () => {
  uni.switchTab({
    url: "/pages/house/index", // 这里可能需要根据实际配置调整
  });
};

// 导航到发布房源页面
const navigateToPublish = () => {
  uni.navigateTo({
    url: "/pages/house/publish/select",
  });
};

// 导航到房产知识详情页
const navigateToGuideDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/guide/detail?id=${id}`,
  });
};

// 导航到房产知识列表
const navigateToGuides = () => {
  uni.navigateTo({
    url: "/pages/house/guide/list",
  });
};

// 导航到其他页面
const navigateTo = (path: string) => {
  uni.navigateTo({
    url: path,
  });
};
</script>

<style lang="scss" scoped>
.house-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.enter-house-index {
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  &:active {
    background-color: #fafafa;
  }
}

.ad-banner-container {
  margin-bottom: 20rpx;
}

.ad-banner-swiper {
  height: 238rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.ad-banner-image {
  width: 100%;
  height: 238rpx;
}

.house-type-tabs {
  .scroll-view-x {
    white-space: nowrap;
  }

  .house-type-list {
    padding: 10rpx 0;
  }

  .house-type-item {
    display: inline-block;
    font-size: 28rpx;
    transition: all 0.3s;
  }
}

.nav-icon-circle {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 40rpx;
}

.guide-image {
  width: 200rpx;
  height: 150rpx;
}

.publisher-entry {
  border-radius: 12rpx;
}

.publish-btn {
  border: none;
  height: 70rpx;
  line-height: 70rpx;
}

.dynamic-list {
  .dynamic-item {
    .avatar {
      width: 80rpx;
      height: 80rpx;
    }

    .verified-badge {
      padding: 4rpx 10rpx;
      background-color: rgba($primary, 0.1);
      color: $primary;
      border-radius: 8rpx;
      font-size: 20rpx;
    }

    .house-tag {
      padding: 4rpx 12rpx;
      background-color: #f0f0f0;
      color: #666;
      border-radius: 6rpx;
      font-size: 22rpx;
    }

    &.border-bottom {
      border-bottom: 1px solid #f0f0f0;
    }
  }
}
</style>
