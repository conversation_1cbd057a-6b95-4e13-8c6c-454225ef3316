<template>
  <view class="home-container">
    <!-- 顶部搜索栏 -->
    <view
      class="header flex items-center justify-between px-30rpx py-16rpx bg-white"
    >
      <view
        class="w-[85%] flex items-center bg-gray-100 rounded-full px-30rpx py-16rpx"
      >
        <text class="i-carbon-search mr-10rpx color-grey"></text>
        <text class="color-grey text-28rpx">搜索商家</text>
      </view>
      <view class="ml-20rpx relative">
        <text class="i-carbon-notification text-40rpx"></text>
        <view v-if="hasNotification" class="notification-dot"></view>
      </view>
    </view>

    <!-- 使用ThorUI的tui-tab组件 -->
    <view class="bg-white">
      <tui-tab
        :tabs="tabs"
        :current="activeTabIndex"
        backgroundColor="#ffffff"
        :size="32"
        color="#666666"
        selectedColor="#333333"
        :sliderBgColor="primaryColor"
        sliderHeight="6rpx"
        sliderWidth="24rpx"
        :bold="true"
        @change="handleTabChange"
      ></tui-tab>
    </view>

    <!-- 内容区域 -->
    <swiper
      class="content-swiper"
      :current="activeTabIndex"
      @change="handleSwiperChange"
    >
      <!-- 推荐页面 -->
      <swiper-item>
        <scroll-view scroll-y class="content-scroll">
          <recommend-content :show-banner="showBanner"></recommend-content>
        </scroll-view>
      </swiper-item>

      <!-- 招聘求职页 -->
      <swiper-item>
        <scroll-view scroll-y class="content-scroll">
          <job-content :show-banner="showBanner"></job-content>
        </scroll-view>
      </swiper-item>

      <!-- 找房页 -->
      <swiper-item>
        <scroll-view scroll-y class="content-scroll">
          <house-content :show-banner="showBanner"></house-content>
        </scroll-view>
      </swiper-item>

      <!-- 相亲交友页 -->
      <swiper-item>
        <scroll-view scroll-y class="content-scroll">
          <dating-content :show-banner="showBanner"></dating-content>
        </scroll-view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script setup lang="ts">
import tuiTab from "@/components/thorui/tui-tab/tui-tab.vue";
import RecommendContent from "@/components/home/<USER>";
import JobContent from "@/components/home/<USER>";
import HouseContent from "@/components/home/<USER>";
import DatingContent from "@/components/home/<USER>";

// 定义标签页
const tabs = [
  { name: "推荐", id: "recommend" },
  { name: "招聘求职", id: "job" },
  { name: "找房", id: "house" },
  { name: "相亲交友", id: "dating" },
];

// 当前激活的标签索引
const activeTabIndex = ref(0);

// 是否有未读通知
const hasNotification = ref(true);

// 是否显示banner广告
const showBanner = ref(true);

// 获取主题色
const primaryColor = ref("#ff6d00");

// 点击标签切换
const handleTabChange = (e: any) => {
  activeTabIndex.value = e.index;
};

// 滑动切换处理
const handleSwiperChange = (e: any) => {
  activeTabIndex.value = e.detail.current;
};
</script>

<style lang="scss" scoped>
.home-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.content-swiper {
  flex: 1;
  height: 0;
}

.content-scroll {
  height: 100%;
}

.notification-dot {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  width: 16rpx;
  height: 16rpx;
  background-color: $primary;
  border-radius: 50%;
}
</style>
