<template>
  <view class="new-house-list">
    <!-- 顶部搜索栏 -->
    <view class="search-header bg-white p-20rpx flex items-center">
      <view class="city-selector flex items-center mr-20rpx">
        <text class="text-32rpx font-bold">北京</text>
        <text class="i-carbon-chevron-down text-24rpx ml-6rpx"></text>
      </view>
      <view
        class="search-box flex-1 flex items-center bg-gray-100 rounded-full px-30rpx py-16rpx"
        @tap="navigateToSearch"
      >
        <text class="i-carbon-search text-28rpx color-grey mr-10rpx"></text>
        <text class="color-grey text-28rpx">搜索楼盘名称</text>
      </view>
      <view class="map-icon ml-20rpx p-10rpx" @tap="navigateToMap">
        <text class="i-carbon-map text-36rpx"></text>
      </view>
    </view>

    <!-- 筛选条件栏 -->
    <HouseFilterBar
      :showHouseType="false"
      :showPrice="true"
      :priceText="'均价'"
      :areaOptions="areaOptions"
      :priceOptions="priceOptions"
      :sortOptions="sortOptions"
      :initialFilters="activeFilters"
      @filter-change="handleFilterChange"
    />

    <!-- 特色功能区 -->
    <view class="feature-grid bg-white px-20rpx py-30rpx mb-20rpx">
      <view class="grid grid-cols-4 gap-30rpx">
        <view
          v-for="(item, index) in featureModules"
          :key="index"
          class="feature-item flex flex-col items-center"
          @tap="applyFilter(item.filter)"
        >
          <view class="feature-icon-box rounded-full" :class="item.bgColor">
            <text :class="[item.icon, 'text-white']"></text>
          </view>
          <text class="feature-name text-26rpx mt-10rpx">{{ item.name }}</text>
        </view>
      </view>
    </view>

    <!-- 房源列表 -->
    <z-paging
      ref="paging"
      v-model="houseList"
      @query="queryHouseList"
      :refresher-enabled="true"
      :show-refresher-update-time="true"
      :auto-scroll-to-top-when-reload="true"
      :empty-view-text="'暂无符合条件的楼盘'"
    >
      <view class="house-list px-20rpx">
        <HouseItem
          v-for="(item, index) in houseList"
          :key="item.id"
          :house="item"
          type="new"
        />
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import HouseFilterBar from "@/components/house/HouseFilterBar.vue";
import HouseItem from "@/components/house/HouseItem.vue";

// z-paging组件引用
const paging = ref(null);

// 房源列表数据
const houseList = ref([]);

// 区域选项
const areaOptions = [
  { label: "不限", value: "" },
  { label: "朝阳区", value: "chaoyang" },
  { label: "海淀区", value: "haidian" },
  { label: "东城区", value: "dongcheng" },
  { label: "西城区", value: "xicheng" },
  { label: "丰台区", value: "fengtai" },
  { label: "石景山区", value: "shijingshan" },
  { label: "通州区", value: "tongzhou" },
  { label: "昌平区", value: "changping" },
  { label: "大兴区", value: "daxing" },
  { label: "顺义区", value: "shunyi" },
  { label: "房山区", value: "fangshan" },
];

// 价格选项
const priceOptions = [
  { label: "不限", value: "" },
  { label: "2万以下", value: "0,20000" },
  { label: "2-3万", value: "20000,30000" },
  { label: "3-4万", value: "30000,40000" },
  { label: "4-5万", value: "40000,50000" },
  { label: "5-6万", value: "50000,60000" },
  { label: "6-8万", value: "60000,80000" },
  { label: "8万以上", value: "80000," },
];

// 排序选项
const sortOptions = [
  { label: "默认排序", value: "default" },
  { label: "均价从低到高", value: "price_asc" },
  { label: "均价从高到低", value: "price_desc" },
  { label: "开盘时间最新", value: "time_desc" },
  { label: "热门程度优先", value: "hot" },
];

// 筛选条件
const activeFilters = reactive({
  area: "",
  price: "",
  sort: "",
  more: {},
});

// 特色功能模块
const featureModules = [
  {
    name: "近期开盘",
    icon: "i-carbon-calendar",
    bgColor: "bg-blue-500",
    filter: { tag: "recent" },
  },
  {
    name: "品牌开发商",
    icon: "i-carbon-building",
    bgColor: "bg-green-500",
    filter: { tag: "brand" },
  },
  {
    name: "低总价",
    icon: "i-carbon-money",
    bgColor: "bg-orange-500",
    filter: { tag: "lowprice" },
  },
  {
    name: "精装修",
    icon: "i-carbon-home",
    bgColor: "bg-red-500",
    filter: { tag: "decoration" },
  },
];

// 页面加载
onLoad((options: any) => {
  // 解析URL参数
  if (options) {
    if (options.area) {
      const area = areaOptions.find((a) => a.value === options.area);
      if (area) {
        activeFilters.area = area.label;
      }
    }

    if (options.filter) {
      applySpecialFilter(options.filter);
    }
  }

  // 首次加载数据
  if (paging.value) {
    paging.value.reload();
  }
});

// 处理特殊筛选参数
const applySpecialFilter = (filterType: string) => {
  switch (filterType) {
    case "recent":
      activeFilters.more = { ...activeFilters.more, isRecent: true };
      break;
    case "brand":
      activeFilters.more = { ...activeFilters.more, isBrand: true };
      break;
    case "lowprice":
      activeFilters.price = "3万以下";
      break;
    case "decoration":
      activeFilters.more = { ...activeFilters.more, isDecoration: true };
      break;
  }
};

// 应用筛选条件
const applyFilter = (filter: any) => {
  // 合并筛选条件
  Object.assign(activeFilters, filter);

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 处理筛选条件变更
const handleFilterChange = (filters: any) => {
  // 更新筛选条件
  Object.assign(activeFilters, filters);

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 获取房源列表数据
const queryHouseList = (pageNo: number, pageSize: number) => {
  // 这里使用setTimeout模拟异步API请求
  setTimeout(() => {
    // 构建查询参数
    const params = {
      page: pageNo,
      pageSize: pageSize,
      area: activeFilters.area || "",
      price: activeFilters.price || "",
      sort: activeFilters.sort || "default",
      ...activeFilters.more,
    };

    // 模拟API返回数据
    const list = Array.from({ length: pageSize }, (_, i) => {
      const index = (pageNo - 1) * pageSize + i;
      return {
        id: `new${index}`,
        name: `${activeFilters.area || "北京"}碧桂园·蓝湾${
          Math.floor(Math.random() * 10) + 1
        }号院`,
        image: `https://picsum.photos/seed/newhouse${index}/300/200`,
        location: `${activeFilters.area || "北京"} · ${
          ["滨江花园", "中央公园", "科技新城", "商务区"][
            Math.floor(Math.random() * 4)
          ]
        }`,
        price: Math.floor(Math.random() * 30000) + 20000,
        tags: [
          "近地铁",
          "学区房",
          "公园旁",
          "精装修",
          "品牌开发商",
          "低总价",
        ].slice(0, Math.floor(Math.random() * 4) + 1),
        status: ["在售", "即将开盘", "售罄"][Math.floor(Math.random() * 3)],
        openTime: `${Math.floor(Math.random() * 12) + 1}月${
          Math.floor(Math.random() * 28) + 1
        }日`,
        extraInfo: `关注${Math.floor(Math.random() * 1000) + 100}人`,
      };
    });

    // 更新z-paging组件数据
    if (paging.value) {
      paging.value.complete(list);
    }
  }, 500);
};

// 导航到搜索页
const navigateToSearch = () => {
  uni.navigateTo({
    url: "/pages/house/search?type=new",
  });
};

// 导航到地图找房
const navigateToMap = () => {
  uni.navigateTo({
    url: "/pages/house/map?type=new",
  });
};
</script>

<style lang="scss" scoped>
.new-house-list {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-header {
  position: sticky;
  top: 0;
  z-index: 100;
}

.feature-icon-box {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
}

.house-list {
  padding-bottom: 30rpx;
}
</style>
