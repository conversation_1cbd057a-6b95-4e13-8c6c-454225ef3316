<template>
  <view class="house-detail">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view
        class="status-bar"
        :style="{ height: statusBarHeight + 'px' }"
      ></view>
      <view class="nav-content flex items-center">
        <view class="back-btn" @tap="goBack">
          <text class="i-carbon-chevron-left"></text>
        </view>
        <view class="tab-container flex">
          <view
            v-for="(tab, index) in tabs"
            :key="index"
            class="tab-item"
            :class="{ active: currentTab === index }"
            @tap="switchTab(index)"
          >
            {{ tab }}
          </view>
        </view>
        <view class="action-btn">
          <text class="i-carbon-overflow-menu-horizontal"></text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view
      class="content-scroll"
      scroll-y
      :scroll-into-view="scrollToId"
      :style="{ height: contentHeight + 'px' }"
    >
      <!-- 轮播图 -->
      <swiper
        class="image-swiper"
        circular
        :indicator-dots="true"
        :autoplay="false"
      >
        <swiper-item v-for="(image, index) in houseDetail.images" :key="index">
          <image
            :src="image"
            mode="aspectFill"
            class="house-image"
            @tap="previewImage(index)"
          />
        </swiper-item>
      </swiper>

      <!-- 房源基本信息 -->
      <view class="info-section bg-white p-30rpx" id="base-info">
        <view class="title-row">
          <text class="house-title">{{ houseDetail.title }}</text>
          <view class="tag-container flex">
            <view v-if="houseDetail.isVip" class="tag vip-tag">
              <text class="i-carbon-star-filled mr-4rpx"></text>
              <text>自营</text>
            </view>
            <view v-if="houseDetail.isNew" class="tag new-tag ml-10rpx"
              >新上</view
            >
          </view>
        </view>

        <view class="price-row flex items-center justify-between mt-20rpx">
          <view class="price-wrapper">
            <text class="price">{{ houseDetail.price }}</text>
            <text class="price-unit">{{ houseDetail.priceUnit }}</text>
          </view>
          <view class="view-count">
            <text class="i-carbon-view mr-6rpx"></text>
            <text>{{ houseDetail.viewCount }}人看过</text>
          </view>
        </view>

        <view class="features-row flex flex-wrap mt-20rpx">
          <view class="feature-item">
            <text class="i-carbon-home mr-6rpx"></text>
            <text>{{ houseDetail.houseType }}</text>
          </view>
          <view class="feature-item">
            <text class="i-carbon-area mr-6rpx"></text>
            <text>{{ houseDetail.area }}㎡</text>
          </view>
          <view class="feature-item">
            <text class="i-carbon-compass mr-6rpx"></text>
            <text>{{ houseDetail.orientation }}</text>
          </view>
          <view class="feature-item">
            <text class="i-carbon-building mr-6rpx"></text>
            <text>{{ houseDetail.floor }}</text>
          </view>
          <view class="feature-item">
            <text class="i-carbon-paint-brush mr-6rpx"></text>
            <text>{{ houseDetail.decoration }}</text>
          </view>
          <view class="feature-item">
            <text class="i-carbon-calendar mr-6rpx"></text>
            <text>{{ houseDetail.rentType }}</text>
          </view>
        </view>

        <view class="tags-row flex flex-wrap mt-20rpx">
          <view
            v-for="(tag, index) in houseDetail.tags"
            :key="index"
            class="tag-item"
          >
            {{ tag }}
          </view>
        </view>
      </view>

      <!-- 地址位置 -->
      <view class="location-section bg-white p-30rpx mt-20rpx" id="location">
        <view class="section-header">
          <text class="section-title">地址位置</text>
        </view>
        <view class="location-detail mt-20rpx">
          <view class="address-row flex">
            <text class="i-carbon-location color-grey"></text>
            <text class="address-text ml-10rpx">{{
              houseDetail.addressDetail
            }}</text>
          </view>
          <view class="map-container mt-20rpx" @tap="openMap">
            <image
              src="/static/images/map-placeholder.jpg"
              mode="aspectFill"
              class="map-image"
            />
            <view class="map-mask flex items-center justify-center">
              <text>查看地图</text>
              <text class="i-carbon-arrow-right ml-6rpx"></text>
            </view>
          </view>
        </view>

        <!-- 周边设施 -->
        <view class="surroundings mt-30rpx">
          <text class="sub-title">周边设施</text>
          <view class="surroundings-list mt-20rpx">
            <view
              v-for="(item, index) in houseDetail.surroundings"
              :key="index"
              class="surrounding-item flex justify-between"
            >
              <view class="surrounding-name">
                <text class="type-tag" :class="getTypeClass(item.type)">{{
                  item.type
                }}</text>
                <text class="ml-10rpx">{{ item.name }}</text>
              </view>
              <text class="distance">{{ item.distance }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 房源描述 -->
      <view
        class="description-section bg-white p-30rpx mt-20rpx"
        id="description"
      >
        <view class="section-header">
          <text class="section-title">房源描述</text>
        </view>
        <view class="description-content mt-20rpx">
          <text class="description-text">{{ houseDetail.description }}</text>
        </view>
      </view>

      <!-- 房屋配置 -->
      <view
        class="facilities-section bg-white p-30rpx mt-20rpx"
        id="facilities"
      >
        <view class="section-header">
          <text class="section-title">房屋配置</text>
        </view>
        <view class="facilities-list flex flex-wrap mt-20rpx">
          <view
            v-for="(facility, index) in facilitiesList"
            :key="index"
            class="facility-item flex flex-col items-center"
            :class="{ available: facilityAvailable(facility.id) }"
          >
            <text :class="[facility.icon, 'facility-icon']"></text>
            <text class="facility-name">{{ facility.name }}</text>
          </view>
        </view>
      </view>

      <!-- 房源信息 -->
      <view
        class="house-info-section bg-white p-30rpx mt-20rpx"
        id="house-info"
      >
        <view class="section-header">
          <text class="section-title">房源信息</text>
        </view>
        <view class="info-list mt-20rpx">
          <view class="info-item flex justify-between">
            <text class="info-label">看房时间</text>
            <text class="info-value">{{ houseDetail.viewingTime }}</text>
          </view>
          <view class="info-item flex justify-between">
            <text class="info-label">入住时间</text>
            <text class="info-value">{{ houseDetail.checkInTime }}</text>
          </view>
          <view class="info-item flex justify-between">
            <text class="info-label">支付方式</text>
            <text class="info-value">{{ houseDetail.paymentMethod }}</text>
          </view>
          <view class="info-item flex justify-between">
            <text class="info-label">所在小区</text>
            <text class="info-value">{{ houseDetail.estateInfo.name }}</text>
          </view>
          <view class="info-item flex justify-between">
            <text class="info-label">建筑年代</text>
            <text class="info-value"
              >{{ houseDetail.estateInfo.buildYear }}年</text
            >
          </view>
          <view class="info-item flex justify-between">
            <text class="info-label">物业公司</text>
            <text class="info-value">{{
              houseDetail.estateInfo.propertyCompany
            }}</text>
          </view>
          <view class="info-item flex justify-between">
            <text class="info-label">物业费</text>
            <text class="info-value"
              >{{ houseDetail.estateInfo.propertyFee }}元/㎡/月</text
            >
          </view>
        </view>
      </view>

      <!-- 猜你喜欢 -->
      <view class="recommend-section bg-white p-30rpx mt-20rpx pb-120rpx">
        <view class="section-header">
          <text class="section-title">猜你喜欢</text>
        </view>
        <view class="recommend-list mt-20rpx">
          <house-item
            v-for="item in recommendHouses"
            :key="item.id"
            :house="item"
            class="recommend-item mb-20rpx"
            @tap="navigateToHouse(item.id)"
          ></house-item>
        </view>
      </view>
    </scroll-view>

    <!-- 底部联系区域 -->
    <view class="contact-bar flex items-center">
      <view class="contact-info flex items-center flex-1">
        <image
          :src="contact.avatar || '/static/images/avatar-placeholder.png'"
          class="contact-avatar"
          mode="aspectFill"
        />
        <view class="contact-name-wrapper ml-20rpx">
          <view class="contact-name">{{ houseDetail.contactPerson }}</view>
          <view class="contact-role">{{ contact.role }}</view>
        </view>
      </view>
      <view class="action-btns flex">
        <button class="message-btn" @tap="sendMessage">
          <text class="i-carbon-chat mr-6rpx"></text>
          <text>在线咨询</text>
        </button>
        <button class="call-btn" @tap="makeCall">
          <text class="i-carbon-phone-filled mr-6rpx"></text>
          <text>电话联系</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from "vue";
import { houseListData, generateMoreHouses } from "@/utils/data/houseListData";
import { facilities } from "@/utils/data/houseData";
import HouseItem from "@/components/house/HouseItem.vue";

// 标签页
const tabs = ["房源", "位置", "描述", "配置", "信息"];
const currentTab = ref(0);
const scrollToId = computed(() => {
  const ids = [
    "base-info",
    "location",
    "description",
    "facilities",
    "house-info",
  ];
  return ids[currentTab.value];
});

// 状态栏高度
const statusBarHeight = ref(20);
const contentHeight = ref(0);

// 房源详情
const houseId = ref("");
const houseDetail = ref(houseListData[0]);
const recommendHouses = ref([]);
const facilitiesList = ref(facilities);

// 联系人信息
const contact = ref({
  avatar: "/static/images/agent-avatar.png",
  role: "置业顾问",
});

// 初始化
onLoad((options: any) => {
  if (options?.id) {
    houseId.value = options.id;
    loadHouseDetail(options.id);
  }

  // 获取状态栏高度
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight;
      contentHeight.value = res.windowHeight - 44 - statusBarHeight.value - 100; // 减去导航栏和底部联系栏的高度
    },
  });

  // 加载推荐房源
  loadRecommendHouses();
});

// 监听标签切换
watch(currentTab, (newVal) => {
  // 可以在这里实现平滑滚动
});

// 切换标签
const switchTab = (index: number) => {
  currentTab.value = index;
};

// 加载房源详情
const loadHouseDetail = (id: string) => {
  // 模拟接口请求
  const house = houseListData.find((item) => item.id === id);
  if (house) {
    houseDetail.value = house;
  } else {
    // 如果找不到，使用第一个房源作为默认
    houseDetail.value = houseListData[0];
  }
};

// 加载推荐房源
const loadRecommendHouses = () => {
  // 随机生成3个推荐房源
  recommendHouses.value = generateMoreHouses(3);
};

// 判断设施是否可用
const facilityAvailable = (id: number) => {
  return houseDetail.value.facilities.includes(id);
};

// 获取周边设施类型的样式类
const getTypeClass = (type: string) => {
  const typeMap: Record<string, string> = {
    交通: "type-traffic",
    教育: "type-education",
    购物: "type-shopping",
    餐饮: "type-food",
    医疗: "type-medical",
    景点: "type-scenic",
    公司: "type-company",
  };
  return typeMap[type] || "type-other";
};

// 打开地图
const openMap = () => {
  const { latitude, longitude } = houseDetail.value.location_coordinates;
  uni.openLocation({
    latitude,
    longitude,
    name: houseDetail.value.estateInfo.name,
    address: houseDetail.value.addressDetail,
  });
};

// 预览图片
const previewImage = (index: number) => {
  uni.previewImage({
    current: houseDetail.value.images[index],
    urls: houseDetail.value.images,
  });
};

// 发送消息
const sendMessage = () => {
  uni.navigateTo({
    url: `/pages/message/chat?targetId=${houseDetail.value.contactPerson}&houseId=${houseDetail.value.id}`,
  });
};

// 拨打电话
const makeCall = () => {
  uni.showActionSheet({
    itemList: ["拨打电话", "复制号码"],
    success: (res) => {
      if (res.tapIndex === 0) {
        uni.makePhoneCall({
          phoneNumber: houseDetail.value.contactPhone.replace(/\*+/g, "0"),
          fail: () => {
            uni.showToast({
              title: "拨号取消或失败",
              icon: "none",
            });
          },
        });
      } else if (res.tapIndex === 1) {
        uni.setClipboardData({
          data: houseDetail.value.contactPhone.replace(/\*+/g, "0"),
          success: () => {
            uni.showToast({
              title: "号码已复制",
              icon: "success",
            });
          },
        });
      }
    },
  });
};

// 跳转到其他房源
const navigateToHouse = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/detail?id=${id}`,
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.house-detail {
  min-height: 100vh;
  background-color: #f8f9fa;
  position: relative;
}

/* 导航栏样式 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.nav-content {
  height: 44px;
  position: relative;
}

.back-btn {
  position: absolute;
  left: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40rpx;
  height: 60rpx;
  width: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-container {
  flex: 1;
  display: flex;
  justify-content: center;
}

.tab-item {
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #666;
  height: 44px;
  line-height: 44px;
  position: relative;

  &.active {
    color: $primary;
    font-weight: 500;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40rpx;
      height: 4rpx;
      background-color: $primary;
      border-radius: 2rpx;
    }
  }
}

.action-btn {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 36rpx;
  height: 60rpx;
  width: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域样式 */
.content-scroll {
  margin-top: calc(44px + var(--status-bar-height));
  padding-bottom: 100rpx;
}

.image-swiper {
  width: 100%;
  height: 500rpx;
}

.house-image {
  width: 100%;
  height: 500rpx;
}

/* 基本信息样式 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.house-title {
  flex: 1;
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-right: 20rpx;
}

.tag-container {
  flex-shrink: 0;
  display: flex;
}

.tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  display: flex;
  align-items: center;
}

.vip-tag {
  background-color: $primary;
  color: white;
}

.new-tag {
  background-color: #f74f55;
  color: white;
}

.price-row {
  margin-top: 30rpx;
}

.price-wrapper {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 40rpx;
  font-weight: bold;
  color: $primary;
}

.price-unit {
  font-size: 28rpx;
  color: $primary;
  margin-left: 4rpx;
}

.view-count {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
}

.features-row {
  margin-top: 30rpx;
}

.feature-item {
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 16rpx;
}

.tags-row {
  margin-top: 20rpx;
}

.tag-item {
  font-size: 24rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 8rpx 20rpx;
  border-radius: 4rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

/* 地址位置样式 */
.address-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

.map-container {
  width: 100%;
  height: 300rpx;
  position: relative;
  overflow: hidden;
  border-radius: 12rpx;
}

.map-image {
  width: 100%;
  height: 100%;
}

.map-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28rpx;
}

.sub-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.surroundings-list {
  margin-top: 20rpx;
}

.surrounding-item {
  padding: 16rpx 0;
  border-bottom: 1px solid #f5f5f5;
  font-size: 28rpx;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.surrounding-name {
  display: flex;
  align-items: center;
}

.type-tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  text-align: center;
  min-width: 80rpx;
}

.type-traffic {
  background-color: rgba(255, 95, 87, 0.1);
  color: #ff5f57;
}

.type-education {
  background-color: rgba(59, 124, 255, 0.1);
  color: #3b7cff;
}

.type-shopping {
  background-color: rgba(255, 164, 38, 0.1);
  color: #ffa426;
}

.type-food {
  background-color: rgba(0, 181, 120, 0.1);
  color: #00b578;
}

.type-medical {
  background-color: rgba(242, 92, 255, 0.1);
  color: #f25cff;
}

.type-scenic {
  background-color: rgba(38, 196, 255, 0.1);
  color: #26c4ff;
}

.type-company {
  background-color: rgba(106, 54, 202, 0.1);
  color: #6a36ca;
}

.type-other {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.distance {
  color: #999;
}

/* 房源描述样式 */
.description-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 房屋配置样式 */
.facilities-list {
  margin-top: 30rpx;
}

.facility-item {
  width: 20%;
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.5;

  &.available {
    opacity: 1;
  }
}

.facility-icon {
  font-size: 48rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.facility-name {
  font-size: 24rpx;
  color: #666;
}

/* 房源信息样式 */
.info-list {
  margin-top: 20rpx;
}

.info-item {
  padding: 20rpx 0;
  border-bottom: 1px solid #f5f5f5;
  font-size: 28rpx;
}

.info-label {
  color: #999;
}

.info-value {
  color: #333;
}

/* 底部联系栏样式 */
.contact-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.contact-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}

.contact-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.contact-role {
  font-size: 22rpx;
  color: #999;
}

.action-btns {
  display: flex;
}

button {
  padding: 0;
  background-color: transparent;
  border: none;
  line-height: 1;
  margin: 0;
}

button::after {
  border: none;
}

.message-btn {
  height: 70rpx;
  padding: 0 30rpx;
  background-color: rgba(255, 109, 0, 0.1);
  color: $primary;
  border-radius: 35rpx;
  font-size: 26rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.call-btn {
  height: 70rpx;
  padding: 0 40rpx;
  background-color: $primary;
  color: white;
  border-radius: 35rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
