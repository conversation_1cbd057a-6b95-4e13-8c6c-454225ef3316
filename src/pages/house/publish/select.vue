<template>
  <view class="publish-select">
    <view class="header bg-white px-30rpx py-20rpx">
      <view class="text-36rpx font-bold">请选择要发布的房源类型</view>
      <view class="text-28rpx color-grey mt-10rpx"
        >选择符合您实际情况的房源类型进行发布</view
      >
    </view>

    <view class="house-type-list px-30rpx py-20rpx">
      <!-- 新房发布 -->
      <view
        class="type-card bg-white rounded-lg mb-30rpx overflow-hidden"
        @tap="navigateToPublish('new')"
      >
        <view class="flex items-center p-30rpx">
          <view class="icon-box bg-red-50 rounded-full p-20rpx">
            <text class="i-carbon-building text-50rpx color-red"></text>
          </view>
          <view class="ml-30rpx flex-1">
            <view class="text-34rpx font-bold">发布新房</view>
            <view class="text-26rpx color-grey mt-6rpx"
              >适用于开发商、销售代理发布新房楼盘信息</view
            >
          </view>
          <text class="i-carbon-chevron-right text-40rpx color-grey"></text>
        </view>
      </view>

      <!-- 二手房发布 -->
      <view
        class="type-card bg-white rounded-lg mb-30rpx overflow-hidden"
        @tap="navigateToPublish('second')"
      >
        <view class="flex items-center p-30rpx">
          <view class="icon-box bg-blue-50 rounded-full p-20rpx">
            <text class="i-carbon-home text-50rpx color-blue"></text>
          </view>
          <view class="ml-30rpx flex-1">
            <view class="text-34rpx font-bold">发布二手房</view>
            <view class="text-26rpx color-grey mt-6rpx"
              >适用于业主、经纪人发布二手房出售信息</view
            >
          </view>
          <text class="i-carbon-chevron-right text-40rpx color-grey"></text>
        </view>
      </view>

      <!-- 租房发布 -->
      <view
        class="type-card bg-white rounded-lg mb-30rpx overflow-hidden"
        @tap="navigateToPublish('rent')"
      >
        <view class="flex items-center p-30rpx">
          <view class="icon-box bg-green-50 rounded-full p-20rpx">
            <text class="i-carbon-apartment text-50rpx color-green"></text>
          </view>
          <view class="ml-30rpx flex-1">
            <view class="text-34rpx font-bold">发布出租房</view>
            <view class="text-26rpx color-grey mt-6rpx"
              >适用于业主、经纪人发布房屋出租信息</view
            >
          </view>
          <text class="i-carbon-chevron-right text-40rpx color-grey"></text>
        </view>
      </view>

      <!-- 商铺办公发布 -->
      <view
        class="type-card bg-white rounded-lg mb-30rpx overflow-hidden"
        @tap="navigateToPublish('commercial')"
      >
        <view class="flex items-center p-30rpx">
          <view class="icon-box bg-orange-50 rounded-full p-20rpx">
            <text class="i-carbon-store text-50rpx color-orange"></text>
          </view>
          <view class="ml-30rpx flex-1">
            <view class="text-34rpx font-bold">发布商铺/办公</view>
            <view class="text-26rpx color-grey mt-6rpx"
              >适用于发布商铺、写字楼、厂房、仓库等信息</view
            >
          </view>
          <text class="i-carbon-chevron-right text-40rpx color-grey"></text>
        </view>
      </view>
    </view>

    <view class="tips p-30rpx bg-white">
      <view class="text-30rpx font-bold mb-20rpx">发布须知</view>
      <view class="tips-content text-26rpx color-grey">
        <view class="tips-item mb-10rpx"
          >• 请确保您发布的房源信息真实有效，严禁虚假房源</view
        >
        <view class="tips-item mb-10rpx">• 首次发布房源需要完成身份认证</view>
        <view class="tips-item mb-10rpx"
          >• 专业经纪人请先完成经纪人认证，获得更多展示机会</view
        >
        <view class="tips-item mb-10rpx"
          >• 发布内容违规将被平台处理，情节严重将被永久封号</view
        >
      </view>
    </view>

    <view class="my-publish-btn fixed-bottom">
      <button
        class="bg-primary text-white w-100% border-none"
        @tap="navigateToMyPublish"
      >
        我的房源发布记录
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";

// 导航到发布页
const navigateToPublish = (type: string) => {
  // 检查用户是否已登录
  const isLoggedIn = uni.getStorageSync("isLoggedIn") || false;
  if (!isLoggedIn) {
    uni.showModal({
      title: "提示",
      content: "发布房源需要先登录账号",
      confirmText: "去登录",
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: "/pages/login/login?redirect=/pages/house/publish/select",
          });
        }
      },
    });
    return;
  }

  // 导航到相应发布页面
  uni.navigateTo({
    url: `/pages/house/publish/form?type=${type}`,
  });
};

// 导航到我的发布记录
const navigateToMyPublish = () => {
  const isLoggedIn = uni.getStorageSync("isLoggedIn") || false;
  if (!isLoggedIn) {
    uni.showModal({
      title: "提示",
      content: "查看发布记录需要先登录账号",
      confirmText: "去登录",
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: "/pages/login/login?redirect=/pages/house/publish/my",
          });
        }
      },
    });
    return;
  }

  uni.navigateTo({
    url: "/pages/house/publish/my",
  });
};
</script>

<style lang="scss" scoped>
.publish-select {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 120rpx;
}

.header {
  border-bottom: 1rpx solid #f0f0f0;
}

.type-card {
  transition: all 0.2s;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  &:hover,
  &:active {
    transform: translateY(-3rpx);
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  }
}

.tips {
  margin-bottom: 120rpx;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;

  button {
    height: 88rpx;
    line-height: 88rpx;
    font-size: 32rpx;
    font-weight: 500;
    border-radius: 44rpx;
  }
}

.color-red {
  color: #f44336;
}

.bg-red-50 {
  background-color: #ffebee;
}

.color-blue {
  color: #2196f3;
}

.bg-blue-50 {
  background-color: #e3f2fd;
}

.color-green {
  color: #4caf50;
}

.bg-green-50 {
  background-color: #e8f5e9;
}

.color-orange {
  color: #ff9800;
}

.bg-orange-50 {
  background-color: #fff3e0;
}
</style>
