<template>
  <view class="second-house-list">
    <!-- 顶部搜索栏 -->
    <view class="search-header bg-white p-20rpx flex items-center">
      <view class="city-selector flex items-center mr-20rpx">
        <text class="text-32rpx font-bold">北京</text>
        <text class="i-carbon-chevron-down text-24rpx ml-6rpx"></text>
      </view>
      <view
        class="search-box flex-1 flex items-center bg-gray-100 rounded-full px-30rpx py-16rpx"
        @tap="navigateToSearch"
      >
        <text class="i-carbon-search text-28rpx color-grey mr-10rpx"></text>
        <text class="color-grey text-28rpx">找小区、找位置</text>
      </view>
      <view class="map-icon ml-20rpx p-10rpx" @tap="navigateToMap">
        <text class="i-carbon-map text-36rpx"></text>
      </view>
    </view>

    <!-- 筛选条件栏 -->
    <HouseFilterBar
      :showHouseType="true"
      :areaOptions="areaOptions"
      :priceOptions="priceOptions"
      :houseTypeOptions="houseTypeOptions"
      :sortOptions="sortOptions"
      :initialFilters="activeFilters"
      @filter-change="handleFilterChange"
    />

    <!-- 特色功能区 -->
    <view class="feature-grid bg-white px-20rpx py-30rpx mb-20rpx">
      <view class="grid grid-cols-4 gap-30rpx">
        <view
          v-for="(item, index) in featureModules"
          :key="index"
          class="feature-item flex flex-col items-center"
          @tap="applyFilter(item.filter)"
        >
          <view class="feature-icon-box rounded-full" :class="item.bgColor">
            <text :class="[item.icon, 'text-white']"></text>
          </view>
          <text class="feature-name text-26rpx mt-10rpx">{{ item.name }}</text>
        </view>
      </view>
    </view>

    <!-- 房源列表 -->
    <z-paging
      ref="paging"
      v-model="houseList"
      @query="queryHouseList"
      :refresher-enabled="true"
      :show-refresher-update-time="true"
      :auto-scroll-to-top-when-reload="true"
      :empty-view-text="'暂无符合条件的房源'"
    >
      <view class="house-list px-20rpx">
        <HouseItem
          v-for="(item, index) in houseList"
          :key="item.id"
          :house="item"
          type="second"
        />
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import HouseFilterBar from "@/components/house/HouseFilterBar.vue";
import HouseItem from "@/components/house/HouseItem.vue";

// z-paging组件引用
const paging = ref(null);

// 房源列表数据
const houseList = ref([]);

// 区域选项
const areaOptions = [
  { label: "不限", value: "" },
  { label: "朝阳区", value: "chaoyang" },
  { label: "海淀区", value: "haidian" },
  { label: "东城区", value: "dongcheng" },
  { label: "西城区", value: "xicheng" },
  { label: "丰台区", value: "fengtai" },
  { label: "石景山区", value: "shijingshan" },
  { label: "通州区", value: "tongzhou" },
  { label: "昌平区", value: "changping" },
  { label: "大兴区", value: "daxing" },
  { label: "顺义区", value: "shunyi" },
  { label: "房山区", value: "fangshan" },
];

// 价格选项
const priceOptions = [
  { label: "不限", value: "" },
  { label: "200万以下", value: "0,200" },
  { label: "200-300万", value: "200,300" },
  { label: "300-500万", value: "300,500" },
  { label: "500-800万", value: "500,800" },
  { label: "800-1000万", value: "800,1000" },
  { label: "1000-1500万", value: "1000,1500" },
  { label: "1500-2000万", value: "1500,2000" },
  { label: "2000万以上", value: "2000," },
];

// 户型选项
const houseTypeOptions = [
  { label: "不限", value: "" },
  { label: "一室", value: "1" },
  { label: "二室", value: "2" },
  { label: "三室", value: "3" },
  { label: "四室", value: "4" },
  { label: "五室+", value: "5+" },
];

// 排序选项
const sortOptions = [
  { label: "默认排序", value: "default" },
  { label: "价格从低到高", value: "price_asc" },
  { label: "价格从高到低", value: "price_desc" },
  { label: "面积从大到小", value: "area_desc" },
  { label: "最新发布", value: "time_desc" },
];

// 筛选条件
const activeFilters = reactive({
  area: "",
  price: "",
  houseType: "",
  sort: "",
  more: {},
});

// 特色功能模块
const featureModules = [
  {
    name: "学区房",
    icon: "i-carbon-education",
    bgColor: "bg-blue-500",
    filter: { tag: "school" },
  },
  {
    name: "近地铁",
    icon: "i-carbon-train",
    bgColor: "bg-green-500",
    filter: { tag: "metro" },
  },
  {
    name: "满五唯一",
    icon: "i-carbon-document",
    bgColor: "bg-yellow-500",
    filter: { tag: "five_only" },
  },
  {
    name: "降价房",
    icon: "i-carbon-cut",
    bgColor: "bg-red-500",
    filter: { tag: "price_cut" },
  },
];

// 页面加载
onLoad((options: any) => {
  // 解析URL参数
  if (options) {
    if (options.area) {
      const area = areaOptions.find((a) => a.value === options.area);
      if (area) {
        activeFilters.area = area.label;
      }
    }

    if (options.filter) {
      applySpecialFilter(options.filter);
    }
  }

  // 首次加载数据
  if (paging.value) {
    paging.value.reload();
  }
});

// 处理特殊筛选参数
const applySpecialFilter = (filterType: string) => {
  switch (filterType) {
    case "school":
      activeFilters.more = { ...activeFilters.more, isSchool: true };
      break;
    case "metro":
      activeFilters.more = { ...activeFilters.more, nearSubway: true };
      break;
    case "price_cut":
      activeFilters.more = { ...activeFilters.more, priceCut: true };
      break;
    case "lowprice":
      activeFilters.price = "300万以下";
      break;
  }
};

// 应用筛选条件
const applyFilter = (filter: any) => {
  // 合并筛选条件
  Object.assign(activeFilters, filter);

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 处理筛选条件变更
const handleFilterChange = (filters: any) => {
  // 更新筛选条件
  Object.assign(activeFilters, filters);

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 获取房源列表数据
const queryHouseList = (pageNo: number, pageSize: number) => {
  // 这里使用setTimeout模拟异步API请求
  setTimeout(() => {
    // 构建查询参数
    const params = {
      page: pageNo,
      pageSize: pageSize,
      area: activeFilters.area || "",
      price: activeFilters.price || "",
      houseType: activeFilters.houseType || "",
      sort: activeFilters.sort || "default",
      ...activeFilters.more,
    };

    // 模拟API返回数据
    const list = Array.from({ length: pageSize }, (_, i) => {
      const index = (pageNo - 1) * pageSize + i;
      return {
        id: `second${index}`,
        title: `${activeFilters.area || "北京"} 精装${
          houseTypeOptions.find((h) => h.label === activeFilters.houseType)
            ?.label || "三室两厅"
        }`,
        image: `https://picsum.photos/seed/house${index}/300/200`,
        layout: `${Math.floor(Math.random() * 3) + 1}室${
          Math.floor(Math.random() * 2) + 1
        }厅${Math.floor(Math.random() * 2) + 1}卫`,
        area: `${Math.floor(Math.random() * 100) + 50}`,
        direction: ["南北通透", "东西向", "朝南", "朝东"][
          Math.floor(Math.random() * 4)
        ],
        floor: `${Math.floor(Math.random() * 20) + 1}层/${
          Math.floor(Math.random() * 30) + 20
        }层`,
        price: Math.floor(Math.random() * 500) + 300,
        unitPrice: `${Math.floor(Math.random() * 20000) + 30000}元/㎡`,
        tags: [
          "满五年",
          "近地铁",
          "学区房",
          "南北通透",
          "精装修",
          "拎包入住",
          "电梯房",
          "随时看房",
        ].slice(0, Math.floor(Math.random() * 4) + 1),
      };
    });

    // 更新z-paging组件数据
    if (paging.value) {
      paging.value.complete(list);
    }
  }, 500);
};

// 导航到搜索页
const navigateToSearch = () => {
  uni.navigateTo({
    url: "/pages/house/search",
  });
};

// 导航到地图找房
const navigateToMap = () => {
  uni.navigateTo({
    url: "/pages/house/map?type=second",
  });
};
</script>

<style lang="scss" scoped>
.second-house-list {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-header {
  position: sticky;
  top: 0;
  z-index: 100;
}

.feature-icon-box {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
}

.house-list {
  padding-bottom: 30rpx;
}
</style>
