<template>
  <view class="rent-house-list">
    <!-- 顶部搜索栏 -->
    <view class="search-header bg-white p-20rpx flex items-center">
      <view class="city-selector flex items-center mr-20rpx">
        <text class="text-32rpx font-bold">北京</text>
        <text class="i-carbon-chevron-down text-24rpx ml-6rpx"></text>
      </view>
      <view
        class="search-box flex-1 flex items-center bg-gray-100 rounded-full px-30rpx py-16rpx"
        @tap="navigateToSearch"
      >
        <text class="i-carbon-search text-28rpx color-grey mr-10rpx"></text>
        <text class="color-grey text-28rpx">小区名称/地址</text>
      </view>
      <view class="map-icon ml-20rpx p-10rpx" @tap="navigateToMap">
        <text class="i-carbon-map text-36rpx"></text>
      </view>
    </view>

    <!-- 筛选条件栏 -->
    <HouseFilterBar
      :showHouseType="true"
      :showRentType="true"
      :areaOptions="areaOptions"
      :priceOptions="priceOptions"
      :houseTypeOptions="houseTypeOptions"
      :rentTypeOptions="rentTypeOptions"
      :sortOptions="sortOptions"
      :initialFilters="activeFilters"
      :priceText="'租金'"
      :rentTypeText="'方式'"
      @filter-change="handleFilterChange"
    />

    <!-- 租房特色功能区 -->
    <view class="rent-features bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm">
      <view class="px-30rpx py-30rpx">
        <text class="text-30rpx font-bold mb-30rpx block">租房专区</text>
        <view class="grid grid-cols-4 gap-20rpx">
          <view
            v-for="(item, index) in rentFeatures"
            :key="index"
            class="feature-card bg-gradient-to-br rounded-xl p-20rpx"
            :class="item.bgClass"
            @tap="applyFilter(item.filter)"
          >
            <view class="feature-icon mb-12rpx">
              <text :class="[item.icon, 'text-white text-32rpx']"></text>
            </view>
            <text class="text-24rpx text-white font-bold">{{ item.name }}</text>
            <text class="text-20rpx text-white text-opacity-80 block mt-4rpx">{{
              item.desc
            }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 品牌公寓推荐 -->
    <view
      class="brand-apartments bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm"
    >
      <view class="px-30rpx py-30rpx">
        <view class="flex justify-between items-center mb-30rpx">
          <text class="text-30rpx font-bold">品牌公寓</text>
          <view class="flex items-center" @tap="navigateToBrandList">
            <text class="text-24rpx color-grey">更多品牌</text>
            <text
              class="i-carbon-chevron-right text-20rpx color-grey ml-6rpx"
            ></text>
          </view>
        </view>
        <scroll-view scroll-x class="brands-scroll">
          <view class="brands-list flex">
            <view
              v-for="(brand, index) in brandApartments"
              :key="index"
              class="brand-item mr-20rpx"
              @tap="filterByBrand(brand)"
            >
              <image
                :src="brand.logo"
                class="brand-logo rounded-lg mb-10rpx"
                mode="aspectFill"
              ></image>
              <text class="text-24rpx text-center block">{{ brand.name }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 房源列表 -->
    <z-paging
      ref="paging"
      v-model="houseList"
      @query="queryHouseList"
      :refresher-enabled="true"
      :show-refresher-update-time="true"
      :auto-scroll-to-top-when-reload="true"
      :empty-view-text="'暂无符合条件的房源'"
    >
      <view class="house-list px-20rpx">
        <HouseItem
          v-for="item in houseList"
          :key="item.id"
          :house="item"
          type="rent"
        />
      </view>
    </z-paging>

    <!-- 筛选弹出层 -->
    <uni-popup ref="areaFilterPopup" type="bottom">
      <view class="filter-popup bg-white p-30rpx">
        <view class="popup-header flex justify-between mb-30rpx">
          <text class="text-32rpx font-bold">选择区域</text>
          <text class="i-carbon-close text-32rpx" @tap="hideAllPopups"></text>
        </view>
        <scroll-view scroll-y style="max-height: 600rpx">
          <view class="area-list">
            <view
              v-for="(area, index) in areaOptions"
              :key="area.value"
              class="area-item py-20rpx"
              :class="{ 'active-filter': activeFilters.area === area.label }"
              @tap="selectArea(area)"
            >
              <text class="text-28rpx">{{ area.label }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>

    <uni-popup ref="rentTypeFilterPopup" type="bottom">
      <view class="filter-popup bg-white p-30rpx">
        <view class="popup-header flex justify-between mb-30rpx">
          <text class="text-32rpx font-bold">选择方式</text>
          <text class="i-carbon-close text-32rpx" @tap="hideAllPopups"></text>
        </view>
        <view class="rent-type-list grid grid-cols-3 gap-20rpx mb-30rpx">
          <view
            v-for="(type, index) in rentTypeOptions"
            :key="type.value"
            class="rent-type-item py-20rpx text-center rounded-lg"
            :class="{
              'active-filter-item': activeFilters.rentType === type.label,
            }"
            @tap="selectRentType(type)"
          >
            <text class="text-28rpx">{{ type.label }}</text>
          </view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="priceFilterPopup" type="bottom">
      <view class="filter-popup bg-white p-30rpx">
        <view class="popup-header flex justify-between mb-30rpx">
          <text class="text-32rpx font-bold">选择租金</text>
          <text class="i-carbon-close text-32rpx" @tap="hideAllPopups"></text>
        </view>
        <scroll-view scroll-y style="max-height: 600rpx">
          <view class="price-list">
            <view
              v-for="(price, index) in priceOptions"
              :key="price.value"
              class="price-item py-20rpx"
              :class="{ 'active-filter': activeFilters.price === price.label }"
              @tap="selectPrice(price)"
            >
              <text class="text-28rpx">{{ price.label }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>

    <uni-popup ref="houseTypeFilterPopup" type="bottom">
      <view class="filter-popup bg-white p-30rpx">
        <view class="popup-header flex justify-between mb-30rpx">
          <text class="text-32rpx font-bold">选择户型</text>
          <text class="i-carbon-close text-32rpx" @tap="hideAllPopups"></text>
        </view>
        <view class="house-type-list grid grid-cols-3 gap-20rpx">
          <view
            v-for="(type, index) in houseTypeOptions"
            :key="type.value"
            class="house-type-item py-20rpx text-center rounded-lg"
            :class="{
              'active-filter-item': activeFilters.houseType === type.label,
            }"
            @tap="selectHouseType(type)"
          >
            <text class="text-28rpx">{{ type.label }}</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import HouseFilterBar from "@/components/house/HouseFilterBar.vue";
import HouseItem from "@/components/house/HouseItem.vue";

// 筛选条件弹出层引用
const areaFilterPopup = ref(null);
const rentTypeFilterPopup = ref(null);
const priceFilterPopup = ref(null);
const houseTypeFilterPopup = ref(null);

// 弹出层显示状态
const showAreaFilter = ref(false);
const showRentTypeFilter = ref(false);
const showPriceFilter = ref(false);
const showHouseTypeFilter = ref(false);
const showMoreFilter = ref(false);

// 分页列表引用
const paging = ref(null);
// 房源列表数据
const houseList = ref([]);

// 活跃的筛选条件
const activeFilters = reactive({
  area: "",
  rentType: "",
  price: "",
  houseType: "",
  sort: "",
  more: {},
});

// 区域选项
const areaOptions = [
  { label: "不限", value: "" },
  { label: "朝阳区", value: "chaoyang" },
  { label: "海淀区", value: "haidian" },
  { label: "东城区", value: "dongcheng" },
  { label: "西城区", value: "xicheng" },
  { label: "丰台区", value: "fengtai" },
  { label: "石景山区", value: "shijingshan" },
  { label: "通州区", value: "tongzhou" },
  { label: "昌平区", value: "changping" },
  { label: "大兴区", value: "daxing" },
  { label: "顺义区", value: "shunyi" },
  { label: "房山区", value: "fangshan" },
];

// 租赁方式选项
const rentTypeOptions = [
  { label: "不限", value: "" },
  { label: "整租", value: "entire" },
  { label: "合租", value: "shared" },
  { label: "公寓", value: "apartment" },
  { label: "短租", value: "short" },
  { label: "日租", value: "daily" },
];

// 价格选项
const priceOptions = [
  { label: "不限", value: "" },
  { label: "800元以下", value: "0,800" },
  { label: "800-1500元", value: "800,1500" },
  { label: "1500-2000元", value: "1500,2000" },
  { label: "2000-3000元", value: "2000,3000" },
  { label: "3000-4000元", value: "3000,4000" },
  { label: "4000-5000元", value: "4000,5000" },
  { label: "5000元以上", value: "5000," },
];

// 户型选项
const houseTypeOptions = [
  { label: "不限", value: "" },
  { label: "一室", value: "1" },
  { label: "二室", value: "2" },
  { label: "三室", value: "3" },
  { label: "四室", value: "4" },
  { label: "五室+", value: "5+" },
];

// 排序选项
const sortOptions = [
  { label: "默认排序", value: "default" },
  { label: "价格从低到高", value: "price_asc" },
  { label: "价格从高到低", value: "price_desc" },
  { label: "面积从大到小", value: "area_desc" },
  { label: "最新发布", value: "time_desc" },
];

// 租房特色功能
const rentFeatures = [
  {
    name: "整租",
    desc: "独享空间",
    icon: "i-carbon-home",
    bgClass: "from-blue-400 to-blue-600",
    filter: { rentType: "整租" },
  },
  {
    name: "合租",
    desc: "经济实惠",
    icon: "i-carbon-group",
    bgClass: "from-green-400 to-green-600",
    filter: { rentType: "合租" },
  },
  {
    name: "近地铁",
    desc: "交通便利",
    icon: "i-carbon-train",
    bgClass: "from-purple-400 to-purple-600",
    filter: { more: { nearSubway: true } },
  },
  {
    name: "拎包入住",
    desc: "家具齐全",
    icon: "i-carbon-shopping-bag",
    bgClass: "from-orange-400 to-orange-600",
    filter: { more: { moveIn: true } },
  },
];

// 品牌公寓数据
const brandApartments = [
  {
    name: "自如",
    logo: "https://picsum.photos/seed/ziru/120/120",
    filter: { brand: "自如" },
  },
  {
    name: "蛋壳公寓",
    logo: "https://picsum.photos/seed/danke/120/120",
    filter: { brand: "蛋壳公寓" },
  },
  {
    name: "青客",
    logo: "https://picsum.photos/seed/qingke/120/120",
    filter: { brand: "青客" },
  },
  {
    name: "魔方公寓",
    logo: "https://picsum.photos/seed/mofang/120/120",
    filter: { brand: "魔方公寓" },
  },
  {
    name: "泊寓",
    logo: "https://picsum.photos/seed/boyu/120/120",
    filter: { brand: "泊寓" },
  },
];

// 监听筛选条件变化
watch(
  [showAreaFilter, showRentTypeFilter, showPriceFilter, showHouseTypeFilter],
  ([areaShow, rentTypeShow, priceShow, houseTypeShow]) => {
    if (areaShow) {
      areaFilterPopup.value.open();
    } else {
      areaFilterPopup.value?.close();
    }

    if (rentTypeShow) {
      rentTypeFilterPopup.value.open();
    } else {
      rentTypeFilterPopup.value?.close();
    }

    if (priceShow) {
      priceFilterPopup.value.open();
    } else {
      priceFilterPopup.value?.close();
    }

    if (houseTypeShow) {
      houseTypeFilterPopup.value.open();
    } else {
      houseTypeFilterPopup.value?.close();
    }
  }
);

// 隐藏所有弹窗
const hideAllPopups = () => {
  showAreaFilter.value = false;
  showRentTypeFilter.value = false;
  showPriceFilter.value = false;
  showHouseTypeFilter.value = false;
  showMoreFilter.value = false;
};

// 选择区域
const selectArea = (area) => {
  activeFilters.area = area.label === "不限" ? "" : area.label;
  hideAllPopups();
  reloadList();
};

// 选择租赁方式
const selectRentType = (type) => {
  activeFilters.rentType = type.label === "不限" ? "" : type.label;
  hideAllPopups();
  reloadList();
};

// 选择价格
const selectPrice = (price) => {
  activeFilters.price = price.label === "不限" ? "" : price.label;
  hideAllPopups();
  reloadList();
};

// 选择户型
const selectHouseType = (type) => {
  activeFilters.houseType = type.label === "不限" ? "" : type.label;
  hideAllPopups();
  reloadList();
};

// 应用筛选条件
const applyFilter = (filter) => {
  // 合并筛选条件
  Object.keys(filter).forEach((key) => {
    if (key === "more") {
      activeFilters.more = { ...activeFilters.more, ...filter.more };
    } else {
      activeFilters[key] = filter[key];
    }
  });

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 处理筛选条件变更
const handleFilterChange = (filters) => {
  // 更新筛选条件
  Object.assign(activeFilters, filters);

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 重新加载列表
const reloadList = () => {
  if (paging.value) {
    paging.value.reload();
  }
};

// 获取租房列表
const queryHouseList = (pageNo, pageSize) => {
  // 模拟异步获取数据
  setTimeout(() => {
    // 构建查询参数
    const params = {
      page: pageNo,
      pageSize: pageSize,
      area: activeFilters.area || "",
      rentType: activeFilters.rentType || "",
      price: activeFilters.price || "",
      houseType: activeFilters.houseType || "",
      sort: activeFilters.sort || "default",
      ...activeFilters.more,
    };

    // 模拟API返回数据
    const list = Array.from({ length: pageSize }, (_, i) => {
      const index = (pageNo - 1) * pageSize + i;
      return {
        id: `rent${index}`,
        title: `${activeFilters.area || "北京"} ${
          ["阳光嘉园", "丽都花园", "保利城", "万科城市之光"][
            Math.floor(Math.random() * 4)
          ]
        } ${["精装", "温馨", "舒适"][Math.floor(Math.random() * 3)]}${
          Math.floor(Math.random() * 3) + 1
        }室`,
        image: `https://picsum.photos/seed/rent${index}/300/200`,
        layout: `${Math.floor(Math.random() * 3) + 1}室${
          Math.floor(Math.random() * 2) + 1
        }厅`,
        area: `${Math.floor(Math.random() * 50) + 30}`,
        direction: ["南北通透", "东西向", "朝南", "朝东"][
          Math.floor(Math.random() * 4)
        ],
        floor: `${Math.floor(Math.random() * 20) + 1}层/${
          Math.floor(Math.random() * 30) + 20
        }层`,
        price: Math.floor(Math.random() * 3000) + 1000,
        rentType:
          activeFilters.rentType ||
          ["整租", "合租", "公寓"][Math.floor(Math.random() * 3)],
        tags: [
          "拎包入住",
          "押一付一",
          "近商圈",
          "交通便利",
          "家电齐全",
          "近地铁",
        ].slice(0, Math.floor(Math.random() * 4) + 1),
        extraInfo: `${Math.floor(Math.random() * 100) + 1}人看过`,
      };
    });

    // 更新z-paging组件数据
    if (paging.value) {
      paging.value.complete(list);
    }
  }, 500);
};

// 导航到搜索页面
const navigateToSearch = () => {
  uni.navigateTo({
    url: "/pages/house/search?type=rent",
  });
};

// 导航到地图找房
const navigateToMap = () => {
  uni.navigateTo({
    url: "/pages/house/map?type=rent",
  });
};

// 导航到品牌列表
const navigateToBrandList = () => {
  uni.navigateTo({
    url: "/pages/house/brand/list",
  });
};

// 按品牌筛选
const filterByBrand = (brand: any) => {
  applyFilter(brand.filter);
};

// 页面加载
onLoad((options: any) => {
  // 解析URL参数
  if (options) {
    if (options.area) {
      const area = areaOptions.find((a) => a.value === options.area);
      if (area) {
        activeFilters.area = area.label;
      }
    }

    if (options.rentType) {
      const rentType = rentTypeOptions.find(
        (r) => r.value === options.rentType
      );
      if (rentType) {
        activeFilters.rentType = rentType.label;
      } else {
        activeFilters.rentType = options.rentType; // 直接使用传入的文本
      }
    }
  }

  // 首次加载数据
  if (paging.value) {
    paging.value.reload();
  }
});
</script>

<style lang="scss" scoped>
.rent-house-list {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-header {
  position: sticky;
  top: 0;
  z-index: 100;
}

.filter-bar {
  position: sticky;
  top: 104rpx; /* 搜索栏高度 */
  z-index: 99;
  border-bottom: 1px solid #f0f0f0;
}

.filter-scroll-view {
  white-space: nowrap;
}

.filter-tabs {
  height: 80rpx;
}

.filter-tab-item {
  font-size: 28rpx;
  color: #333;
  margin-right: 30rpx;
}

.feature-card {
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.brands-scroll {
  white-space: nowrap;
}

.brands-list {
  display: inline-flex;
  padding: 10rpx 0;
}

.brand-item {
  flex-shrink: 0;
  text-align: center;

  &:active {
    opacity: 0.7;
  }
}

.brand-logo {
  width: 80rpx;
  height: 80rpx;
}

.house-list {
  padding-bottom: 30rpx;
}

.tag-item {
  padding: 4rpx 12rpx;
  background-color: #f6f6f6;
  color: #666;
  border-radius: 4rpx;
  font-size: 22rpx;
}

.color-red {
  color: #fa5741;
}

.color-grey {
  color: #666;
}

.active-filter {
  color: $primary;
  font-weight: bold;
}

.active-filter-item {
  color: $primary;
  font-weight: bold;
  background-color: rgba($primary, 0.1);
}

.filter-popup {
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
}
</style>
