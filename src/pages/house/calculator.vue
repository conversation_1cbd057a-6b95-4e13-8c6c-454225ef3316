<template>
  <view class="calculator-page">
    <!-- 顶部导航 -->
    <view class="nav-header bg-white px-20rpx py-20rpx flex items-center shadow-sm">
      <view class="back-btn mr-20rpx" @tap="goBack">
        <text class="i-carbon-arrow-left text-32rpx color-grey"></text>
      </view>
      <text class="text-32rpx font-bold flex-1">房贷计算器</text>
    </view>

    <!-- 计算类型选择 -->
    <view class="calc-type-tabs bg-white mb-20rpx">
      <view class="flex">
        <view
          v-for="(tab, index) in calcTabs"
          :key="index"
          class="tab-item flex-1 text-center py-30rpx"
          :class="{ 'active': currentTab === index }"
          @tap="switchTab(index)"
        >
          <text class="text-28rpx">{{ tab.name }}</text>
        </view>
      </view>
    </view>

    <!-- 商业贷款计算 -->
    <view v-if="currentTab === 0" class="commercial-loan">
      <!-- 贷款信息输入 -->
      <view class="input-section bg-white mb-20rpx">
        <view class="section-title px-30rpx py-20rpx border-bottom">
          <text class="text-30rpx font-bold">贷款信息</text>
        </view>
        
        <view class="input-list">
          <!-- 房屋总价 -->
          <view class="input-item px-30rpx py-30rpx border-bottom">
            <view class="flex justify-between items-center">
              <text class="text-28rpx">房屋总价</text>
              <view class="input-container">
                <input
                  v-model="loanData.totalPrice"
                  type="digit"
                  class="text-input text-right"
                  placeholder="请输入房屋总价"
                />
                <text class="text-26rpx color-grey ml-10rpx">万元</text>
              </view>
            </view>
          </view>

          <!-- 首付比例 -->
          <view class="input-item px-30rpx py-30rpx border-bottom">
            <view class="flex justify-between items-center">
              <text class="text-28rpx">首付比例</text>
              <view class="input-container">
                <input
                  v-model="loanData.downPaymentRatio"
                  type="digit"
                  class="text-input text-right"
                  placeholder="30"
                />
                <text class="text-26rpx color-grey ml-10rpx">%</text>
              </view>
            </view>
          </view>

          <!-- 贷款年限 -->
          <view class="input-item px-30rpx py-30rpx border-bottom">
            <view class="flex justify-between items-center">
              <text class="text-28rpx">贷款年限</text>
              <picker
                mode="selector"
                :range="loanYears"
                :value="loanData.yearIndex"
                @change="onYearChange"
              >
                <view class="picker-text">
                  <text class="text-28rpx color-primary">{{ loanYears[loanData.yearIndex] }}</text>
                  <text class="i-carbon-chevron-down text-20rpx color-grey ml-10rpx"></text>
                </view>
              </picker>
            </view>
          </view>

          <!-- 贷款利率 -->
          <view class="input-item px-30rpx py-30rpx">
            <view class="flex justify-between items-center">
              <text class="text-28rpx">贷款利率</text>
              <view class="input-container">
                <input
                  v-model="loanData.interestRate"
                  type="digit"
                  class="text-input text-right"
                  placeholder="4.9"
                />
                <text class="text-26rpx color-grey ml-10rpx">%</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 还款方式选择 -->
      <view class="repayment-method bg-white mb-20rpx">
        <view class="section-title px-30rpx py-20rpx border-bottom">
          <text class="text-30rpx font-bold">还款方式</text>
        </view>
        <view class="method-list px-30rpx py-20rpx">
          <view
            v-for="(method, index) in repaymentMethods"
            :key="index"
            class="method-item py-20rpx"
            :class="{ 'border-bottom': index < repaymentMethods.length - 1 }"
            @tap="selectRepaymentMethod(index)"
          >
            <view class="flex items-center justify-between">
              <view>
                <text class="text-28rpx font-bold">{{ method.name }}</text>
                <text class="text-24rpx color-grey block mt-6rpx">{{ method.desc }}</text>
              </view>
              <view class="radio-btn" :class="{ 'active': loanData.repaymentMethod === index }">
                <text v-if="loanData.repaymentMethod === index" class="i-carbon-checkmark text-white text-20rpx"></text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 计算按钮 -->
      <view class="calc-btn-container px-30rpx mb-30rpx">
        <button class="calc-btn bg-primary text-white" @tap="calculateLoan">
          开始计算
        </button>
      </view>

      <!-- 计算结果 -->
      <view v-if="showResult" class="result-section bg-white">
        <view class="section-title px-30rpx py-20rpx border-bottom">
          <text class="text-30rpx font-bold">计算结果</text>
        </view>
        
        <view class="result-content px-30rpx py-30rpx">
          <!-- 核心数据 -->
          <view class="core-data mb-40rpx">
            <view class="data-item mb-30rpx">
              <text class="data-label text-26rpx color-grey">贷款总额</text>
              <text class="data-value text-36rpx font-bold color-primary">{{ result.loanAmount }}万元</text>
            </view>
            <view class="data-item mb-30rpx">
              <text class="data-label text-26rpx color-grey">月供金额</text>
              <text class="data-value text-36rpx font-bold color-red">{{ result.monthlyPayment }}元</text>
            </view>
            <view class="data-item mb-30rpx">
              <text class="data-label text-26rpx color-grey">支付利息</text>
              <text class="data-value text-36rpx font-bold">{{ result.totalInterest }}万元</text>
            </view>
            <view class="data-item">
              <text class="data-label text-26rpx color-grey">还款总额</text>
              <text class="data-value text-36rpx font-bold">{{ result.totalPayment }}万元</text>
            </view>
          </view>

          <!-- 详细信息 -->
          <view class="detail-info">
            <view class="info-item flex justify-between py-20rpx border-bottom">
              <text class="text-28rpx">首付金额</text>
              <text class="text-28rpx font-bold">{{ result.downPayment }}万元</text>
            </view>
            <view class="info-item flex justify-between py-20rpx border-bottom">
              <text class="text-28rpx">贷款年限</text>
              <text class="text-28rpx font-bold">{{ loanYears[loanData.yearIndex] }}</text>
            </view>
            <view class="info-item flex justify-between py-20rpx border-bottom">
              <text class="text-28rpx">贷款利率</text>
              <text class="text-28rpx font-bold">{{ loanData.interestRate }}%</text>
            </view>
            <view class="info-item flex justify-between py-20rpx">
              <text class="text-28rpx">还款方式</text>
              <text class="text-28rpx font-bold">{{ repaymentMethods[loanData.repaymentMethod].name }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 公积金贷款计算 -->
    <view v-if="currentTab === 1" class="provident-loan">
      <view class="coming-soon text-center py-100rpx">
        <text class="i-carbon-construction text-80rpx color-grey block mb-20rpx"></text>
        <text class="text-28rpx color-grey">公积金贷款计算功能</text>
        <text class="text-24rpx color-grey block mt-10rpx">即将上线，敬请期待</text>
      </view>
    </view>

    <!-- 组合贷款计算 -->
    <view v-if="currentTab === 2" class="combination-loan">
      <view class="coming-soon text-center py-100rpx">
        <text class="i-carbon-construction text-80rpx color-grey block mb-20rpx"></text>
        <text class="text-28rpx color-grey">组合贷款计算功能</text>
        <text class="text-24rpx color-grey block mt-10rpx">即将上线，敬请期待</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 计算类型标签
const calcTabs = [
  { name: "商业贷款", id: "commercial" },
  { name: "公积金贷款", id: "provident" },
  { name: "组合贷款", id: "combination" }
];

const currentTab = ref(0);

// 贷款年限选项
const loanYears = ["10年", "15年", "20年", "25年", "30年"];

// 还款方式
const repaymentMethods = [
  {
    name: "等额本息",
    desc: "每月还款金额相同，前期利息多，后期本金多"
  },
  {
    name: "等额本金",
    desc: "每月还款本金相同，前期还款多，后期还款少"
  }
];

// 贷款数据
const loanData = reactive({
  totalPrice: "", // 房屋总价（万元）
  downPaymentRatio: "30", // 首付比例（%）
  yearIndex: 4, // 贷款年限索引（默认30年）
  interestRate: "4.9", // 贷款利率（%）
  repaymentMethod: 0 // 还款方式（0: 等额本息, 1: 等额本金）
});

// 计算结果
const result = reactive({
  loanAmount: "0", // 贷款总额
  monthlyPayment: "0", // 月供金额
  totalInterest: "0", // 支付利息
  totalPayment: "0", // 还款总额
  downPayment: "0" // 首付金额
});

const showResult = ref(false);

// 切换标签
const switchTab = (index: number) => {
  currentTab.value = index;
  showResult.value = false;
};

// 选择贷款年限
const onYearChange = (e: any) => {
  loanData.yearIndex = e.detail.value;
};

// 选择还款方式
const selectRepaymentMethod = (index: number) => {
  loanData.repaymentMethod = index;
};

// 计算贷款
const calculateLoan = () => {
  if (!loanData.totalPrice || !loanData.downPaymentRatio || !loanData.interestRate) {
    uni.showToast({
      title: "请填写完整信息",
      icon: "none"
    });
    return;
  }

  const totalPrice = parseFloat(loanData.totalPrice); // 房屋总价（万元）
  const downPaymentRatio = parseFloat(loanData.downPaymentRatio) / 100; // 首付比例
  const yearStr = loanYears[loanData.yearIndex];
  const years = parseInt(yearStr.replace("年", "")); // 贷款年限
  const annualRate = parseFloat(loanData.interestRate) / 100; // 年利率
  const monthlyRate = annualRate / 12; // 月利率
  const totalMonths = years * 12; // 总月数

  // 计算首付和贷款金额
  const downPayment = totalPrice * downPaymentRatio;
  const loanAmount = totalPrice - downPayment;

  let monthlyPayment = 0;
  let totalInterest = 0;

  if (loanData.repaymentMethod === 0) {
    // 等额本息
    if (monthlyRate === 0) {
      monthlyPayment = (loanAmount * 10000) / totalMonths;
    } else {
      monthlyPayment = (loanAmount * 10000 * monthlyRate * Math.pow(1 + monthlyRate, totalMonths)) / 
                      (Math.pow(1 + monthlyRate, totalMonths) - 1);
    }
    totalInterest = (monthlyPayment * totalMonths - loanAmount * 10000) / 10000;
  } else {
    // 等额本金
    const monthlyPrincipal = (loanAmount * 10000) / totalMonths;
    let totalPayment = 0;
    for (let i = 0; i < totalMonths; i++) {
      const remainingPrincipal = loanAmount * 10000 - monthlyPrincipal * i;
      const monthlyInterest = remainingPrincipal * monthlyRate;
      totalPayment += monthlyPrincipal + monthlyInterest;
    }
    monthlyPayment = monthlyPrincipal + (loanAmount * 10000 * monthlyRate); // 首月还款
    totalInterest = (totalPayment - loanAmount * 10000) / 10000;
  }

  // 更新结果
  result.loanAmount = loanAmount.toFixed(2);
  result.monthlyPayment = Math.round(monthlyPayment).toString();
  result.totalInterest = totalInterest.toFixed(2);
  result.totalPayment = (loanAmount + totalInterest).toFixed(2);
  result.downPayment = downPayment.toFixed(2);

  showResult.value = true;
};

// 返回
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.calculator-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.calc-type-tabs {
  .tab-item {
    position: relative;
    
    &.active {
      color: $primary;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background-color: $primary;
        border-radius: 2rpx;
      }
    }
  }
}

.input-container {
  display: flex;
  align-items: center;
}

.text-input {
  font-size: 28rpx;
  color: #333;
  min-width: 200rpx;
}

.picker-text {
  display: flex;
  align-items: center;
}

.method-item {
  &:active {
    background-color: #f9f9f9;
  }
}

.radio-btn {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  
  &.active {
    background-color: $primary;
    border-color: $primary;
  }
}

.calc-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.data-value {
  text-align: right;
}

.border-bottom {
  border-bottom: 1px solid #f0f0f0;
}

.color-grey {
  color: #666;
}

.color-primary {
  color: $primary;
}

.color-red {
  color: #fa5741;
}

.coming-soon {
  background-color: white;
  margin: 20rpx;
  border-radius: 12rpx;
}
</style>
