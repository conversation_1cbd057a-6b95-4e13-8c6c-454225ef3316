<template>
  <view class="search-page">
    <!-- 搜索栏 -->
    <view class="search-header bg-white px-20rpx py-20rpx flex items-center">
      <view class="back-btn mr-20rpx" @tap="goBack">
        <text class="i-carbon-arrow-left text-32rpx color-grey"></text>
      </view>
      <view class="search-input-container flex-1 relative">
        <input
          v-model="searchKeyword"
          class="search-input bg-gray-50 rounded-full px-24rpx py-16rpx text-28rpx"
          placeholder="搜索小区、地标、学校"
          confirm-type="search"
          @confirm="handleSearch"
          @input="handleInput"
          focus
        />
        <view v-if="searchKeyword" class="clear-btn" @tap="clearSearch">
          <text class="i-carbon-close text-24rpx color-grey"></text>
        </view>
      </view>
      <view class="search-btn ml-20rpx" @tap="handleSearch">
        <text class="text-28rpx color-primary">搜索</text>
      </view>
    </view>

    <!-- 搜索建议 -->
    <view v-if="showSuggestions && suggestions.length > 0" class="suggestions bg-white">
      <view
        v-for="(item, index) in suggestions"
        :key="index"
        class="suggestion-item px-30rpx py-24rpx border-bottom"
        @tap="selectSuggestion(item)"
      >
        <view class="flex items-center">
          <text class="i-carbon-location text-24rpx color-grey mr-16rpx"></text>
          <view class="flex-1">
            <text class="text-30rpx color-main">{{ item.name }}</text>
            <text class="text-24rpx color-grey ml-10rpx">{{ item.district }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 热门搜索 -->
    <view v-if="!searchKeyword" class="hot-search bg-white mt-20rpx">
      <view class="px-30rpx py-30rpx">
        <text class="text-32rpx font-bold mb-30rpx block">热门搜索</text>
        <view class="flex flex-wrap">
          <view
            v-for="(tag, index) in hotSearchTags"
            :key="index"
            class="hot-tag mr-20rpx mb-20rpx px-24rpx py-12rpx bg-gray-50 rounded-full"
            @tap="searchByTag(tag)"
          >
            <text class="text-26rpx color-main">{{ tag }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 搜索历史 -->
    <view v-if="!searchKeyword && searchHistory.length > 0" class="search-history bg-white mt-20rpx">
      <view class="px-30rpx py-30rpx">
        <view class="flex justify-between items-center mb-30rpx">
          <text class="text-32rpx font-bold">搜索历史</text>
          <view @tap="clearHistory">
            <text class="i-carbon-trash-can text-24rpx color-grey"></text>
          </view>
        </view>
        <view class="history-list">
          <view
            v-for="(item, index) in searchHistory"
            :key="index"
            class="history-item py-20rpx border-bottom flex items-center justify-between"
            @tap="searchByHistory(item)"
          >
            <view class="flex items-center flex-1">
              <text class="i-carbon-time text-24rpx color-grey mr-16rpx"></text>
              <text class="text-28rpx color-main">{{ item }}</text>
            </view>
            <view @tap.stop="removeHistoryItem(index)">
              <text class="i-carbon-close text-20rpx color-grey"></text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 搜索结果 -->
    <view v-if="showResults" class="search-results">
      <view class="result-header bg-white px-30rpx py-20rpx">
        <text class="text-28rpx color-grey">找到 {{ totalCount }} 个相关结果</text>
      </view>
      
      <!-- 房源列表 -->
      <view class="house-list px-20rpx">
        <house-item
          v-for="house in searchResults"
          :key="house.id"
          :house="house"
          class="mb-20rpx"
          @tap="navigateToDetail(house.id)"
        ></house-item>
      </view>

      <!-- 空状态 -->
      <view v-if="searchResults.length === 0" class="empty-result text-center py-100rpx">
        <text class="i-carbon-search text-80rpx color-grey block mb-20rpx"></text>
        <text class="text-28rpx color-grey">没有找到相关房源</text>
        <text class="text-24rpx color-grey block mt-10rpx">试试其他关键词</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import HouseItem from "@/components/house/HouseItem.vue";
import { generateMoreHouses } from "@/utils/data/houseListData";
import type { HouseItemType } from "@/types/house";

// 搜索关键词
const searchKeyword = ref("");
const showSuggestions = ref(false);
const showResults = ref(false);

// 搜索建议
const suggestions = ref([
  { name: "阳光花园", district: "城中区" },
  { name: "金桥国际", district: "城东区" },
  { name: "碧桂园", district: "城南区" },
  { name: "万达广场", district: "城西区" },
]);

// 热门搜索标签
const hotSearchTags = [
  "学区房", "地铁房", "精装修", "南北通透", 
  "低总价", "满二年", "近商圈", "品牌公寓"
];

// 搜索历史
const searchHistory = ref<string[]>([]);

// 搜索结果
const searchResults = ref<HouseItemType[]>([]);
const totalCount = ref(0);

// 处理输入
const handleInput = () => {
  if (searchKeyword.value.trim()) {
    showSuggestions.value = true;
    showResults.value = false;
  } else {
    showSuggestions.value = false;
    showResults.value = false;
  }
};

// 执行搜索
const handleSearch = () => {
  if (!searchKeyword.value.trim()) return;
  
  // 添加到搜索历史
  addToHistory(searchKeyword.value);
  
  // 模拟搜索结果
  searchResults.value = generateMoreHouses(5);
  totalCount.value = searchResults.value.length;
  
  showSuggestions.value = false;
  showResults.value = true;
};

// 选择搜索建议
const selectSuggestion = (item: any) => {
  searchKeyword.value = item.name;
  handleSearch();
};

// 通过标签搜索
const searchByTag = (tag: string) => {
  searchKeyword.value = tag;
  handleSearch();
};

// 通过历史记录搜索
const searchByHistory = (keyword: string) => {
  searchKeyword.value = keyword;
  handleSearch();
};

// 添加到搜索历史
const addToHistory = (keyword: string) => {
  const history = searchHistory.value;
  const index = history.indexOf(keyword);
  
  if (index > -1) {
    history.splice(index, 1);
  }
  
  history.unshift(keyword);
  
  if (history.length > 10) {
    history.pop();
  }
  
  // 这里应该保存到本地存储
  uni.setStorageSync('house_search_history', history);
};

// 清空搜索
const clearSearch = () => {
  searchKeyword.value = "";
  showSuggestions.value = false;
  showResults.value = false;
};

// 清空历史
const clearHistory = () => {
  searchHistory.value = [];
  uni.removeStorageSync('house_search_history');
};

// 删除历史项
const removeHistoryItem = (index: number) => {
  searchHistory.value.splice(index, 1);
  uni.setStorageSync('house_search_history', searchHistory.value);
};

// 返回
const goBack = () => {
  uni.navigateBack();
};

// 导航到详情页
const navigateToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/detail?id=${id}`,
  });
};

// 页面加载时获取搜索历史
onLoad(() => {
  const history = uni.getStorageSync('house_search_history');
  if (history && Array.isArray(history)) {
    searchHistory.value = history;
  }
});
</script>

<style lang="scss" scoped>
.search-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-input-container {
  position: relative;
}

.search-input {
  width: 100%;
  border: none;
  outline: none;
}

.clear-btn {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  padding: 10rpx;
}

.border-bottom {
  border-bottom: 1px solid #f0f0f0;
}

.hot-tag {
  transition: all 0.2s ease;
  
  &:active {
    background-color: #e5e7eb;
  }
}

.suggestion-item, .history-item {
  &:active {
    background-color: #f9f9f9;
  }
}

.color-main {
  color: #333;
}

.color-grey {
  color: #666;
}

.color-primary {
  color: $primary;
}
</style>
