<template>
  <view class="guide-detail-page">
    <!-- 顶部导航 -->
    <view class="nav-header bg-white px-20rpx py-20rpx flex items-center shadow-sm">
      <view class="back-btn mr-20rpx" @tap="goBack">
        <text class="i-carbon-arrow-left text-32rpx color-grey"></text>
      </view>
      <text class="text-32rpx font-bold flex-1">文章详情</text>
      <view class="share-btn" @tap="shareArticle">
        <text class="i-carbon-share text-28rpx color-grey"></text>
      </view>
    </view>

    <!-- 文章内容 -->
    <scroll-view scroll-y class="content-scroll">
      <view class="article-content bg-white">
        <!-- 文章头部 -->
        <view class="article-header px-30rpx py-30rpx">
          <text class="article-title text-36rpx font-bold line-height-1-4">{{ article.title }}</text>
          
          <view class="article-meta flex items-center justify-between mt-30rpx">
            <view class="flex items-center">
              <image
                :src="article.authorAvatar"
                class="author-avatar rounded-full mr-15rpx"
                mode="aspectFill"
              ></image>
              <view>
                <text class="author-name text-28rpx font-bold">{{ article.author }}</text>
                <text class="publish-time text-24rpx color-grey block mt-6rpx">{{ article.publishTime }}</text>
              </view>
            </view>
            <view class="article-stats flex items-center">
              <view class="stat-item flex items-center mr-30rpx">
                <text class="i-carbon-view text-20rpx color-grey mr-6rpx"></text>
                <text class="text-24rpx color-grey">{{ article.viewCount }}</text>
              </view>
              <view class="stat-item flex items-center">
                <text class="i-carbon-thumbs-up text-20rpx color-grey mr-6rpx"></text>
                <text class="text-24rpx color-grey">{{ article.likeCount }}</text>
              </view>
            </view>
          </view>

          <!-- 文章标签 -->
          <view v-if="article.tags && article.tags.length > 0" class="article-tags mt-30rpx">
            <text
              v-for="(tag, index) in article.tags"
              :key="index"
              class="tag-item mr-15rpx"
            >{{ tag }}</text>
          </view>
        </view>

        <!-- 文章正文 -->
        <view class="article-body px-30rpx pb-30rpx">
          <!-- 文章摘要 -->
          <view class="article-summary bg-gray-50 p-30rpx rounded-lg mb-40rpx">
            <text class="text-28rpx color-grey line-height-1-6">{{ article.summary }}</text>
          </view>

          <!-- 文章内容 -->
          <view class="article-text">
            <rich-text :nodes="article.content"></rich-text>
          </view>

          <!-- 文章图片 -->
          <view v-if="article.images && article.images.length > 0" class="article-images mt-40rpx">
            <view
              v-for="(image, index) in article.images"
              :key="index"
              class="image-container mb-30rpx"
              @tap="previewImage(index)"
            >
              <image
                :src="image"
                class="article-image rounded-lg"
                mode="widthFix"
              ></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 互动区域 -->
      <view class="interaction-section bg-white mt-20rpx">
        <view class="interaction-header px-30rpx py-20rpx border-bottom">
          <text class="text-30rpx font-bold">互动区域</text>
        </view>
        
        <view class="interaction-buttons px-30rpx py-30rpx">
          <view class="flex justify-around">
            <view class="interaction-btn" @tap="toggleLike">
              <view class="btn-icon" :class="{ 'liked': isLiked }">
                <text class="i-carbon-thumbs-up text-32rpx"></text>
              </view>
              <text class="btn-text text-24rpx mt-10rpx">{{ isLiked ? '已点赞' : '点赞' }}</text>
            </view>
            
            <view class="interaction-btn" @tap="toggleCollect">
              <view class="btn-icon" :class="{ 'collected': isCollected }">
                <text class="i-carbon-bookmark text-32rpx"></text>
              </view>
              <text class="btn-text text-24rpx mt-10rpx">{{ isCollected ? '已收藏' : '收藏' }}</text>
            </view>
            
            <view class="interaction-btn" @tap="shareArticle">
              <view class="btn-icon">
                <text class="i-carbon-share text-32rpx color-grey"></text>
              </view>
              <text class="btn-text text-24rpx mt-10rpx">分享</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 相关推荐 -->
      <view class="related-section bg-white mt-20rpx">
        <view class="section-header px-30rpx py-20rpx border-bottom">
          <text class="text-30rpx font-bold">相关推荐</text>
        </view>
        
        <view class="related-articles px-20rpx py-20rpx">
          <view
            v-for="(relatedArticle, index) in relatedArticles"
            :key="index"
            class="related-item p-20rpx mb-20rpx rounded-lg bg-gray-50"
            @tap="navigateToRelated(relatedArticle.id)"
          >
            <view class="flex">
              <image
                :src="relatedArticle.image"
                class="related-image rounded-lg mr-20rpx"
                mode="aspectFill"
              ></image>
              <view class="related-info flex-1">
                <text class="related-title text-28rpx font-bold line-clamp-2">{{ relatedArticle.title }}</text>
                <view class="related-meta flex justify-between items-center mt-15rpx">
                  <text class="author text-24rpx color-grey">{{ relatedArticle.author }}</text>
                  <text class="view-count text-24rpx color-grey">{{ relatedArticle.viewCount }}阅读</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部占位 -->
      <view class="bottom-placeholder h-100rpx"></view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
// 文章数据
const article = ref({
  id: "",
  title: "新手买房必看：购房流程详解",
  author: "房产专家",
  authorAvatar: "https://picsum.photos/seed/expert/100/100",
  publishTime: "2024-01-15",
  viewCount: "25.8万",
  likeCount: "1.2万",
  tags: ["购房指南", "实用技巧", "新手必看"],
  summary: "本文详细介绍了购房的各个环节，从前期准备到最终交房，帮助新手购房者避免常见陷阱，顺利完成购房过程。无论是首次购房还是换房，都能从中获得有价值的指导。",
  content: `
    <p style="font-size: 28rpx; line-height: 1.6; color: #333; margin-bottom: 30rpx;">
      购房是人生中的重要决策，对于新手来说，了解完整的购房流程至关重要。本文将为您详细介绍从看房到交房的每一个步骤。
    </p>
    
    <h3 style="font-size: 32rpx; font-weight: bold; color: #333; margin: 40rpx 0 20rpx 0;">一、前期准备</h3>
    <p style="font-size: 28rpx; line-height: 1.6; color: #333; margin-bottom: 30rpx;">
      1. 确定购房预算：包括首付、月供、税费、装修等各项费用<br/>
      2. 了解购房政策：限购、限贷、税收等相关政策<br/>
      3. 选择合适区域：考虑工作、生活、教育等因素
    </p>
    
    <h3 style="font-size: 32rpx; font-weight: bold; color: #333; margin: 40rpx 0 20rpx 0;">二、看房选房</h3>
    <p style="font-size: 28rpx; line-height: 1.6; color: #333; margin-bottom: 30rpx;">
      看房时要注意房屋的朝向、采光、通风、户型结构等因素，同时了解小区环境、配套设施、交通便利性等。
    </p>
    
    <h3 style="font-size: 32rpx; font-weight: bold; color: #333; margin: 40rpx 0 20rpx 0;">三、签约付款</h3>
    <p style="font-size: 28rpx; line-height: 1.6; color: #333; margin-bottom: 30rpx;">
      签订购房合同时要仔细阅读条款，特别注意交房时间、违约责任、配套设施等内容。
    </p>
  `,
  images: [
    "https://picsum.photos/seed/house_guide1/800/600",
    "https://picsum.photos/seed/house_guide2/800/600"
  ]
});

// 互动状态
const isLiked = ref(false);
const isCollected = ref(false);

// 相关文章
const relatedArticles = ref([
  {
    id: "related1",
    title: "二手房交易注意事项",
    author: "房产顾问",
    image: "https://picsum.photos/seed/related1/200/150",
    viewCount: "18.5万"
  },
  {
    id: "related2", 
    title: "房贷申请完整攻略",
    author: "金融专家",
    image: "https://picsum.photos/seed/related2/200/150",
    viewCount: "22.1万"
  },
  {
    id: "related3",
    title: "装修预算规划指南",
    author: "装修达人",
    image: "https://picsum.photos/seed/related3/200/150",
    viewCount: "15.8万"
  }
]);

// 点赞
const toggleLike = () => {
  isLiked.value = !isLiked.value;
  uni.showToast({
    title: isLiked.value ? '点赞成功' : '取消点赞',
    icon: 'none'
  });
};

// 收藏
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  uni.showToast({
    title: isCollected.value ? '收藏成功' : '取消收藏',
    icon: 'none'
  });
};

// 分享文章
const shareArticle = () => {
  uni.share({
    provider: "weixin",
    scene: "WXSceneSession",
    type: 0,
    href: `https://example.com/article/${article.value.id}`,
    title: article.value.title,
    summary: article.value.summary,
    imageUrl: article.value.images[0] || "",
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      });
    },
    fail: () => {
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      });
    }
  });
};

// 预览图片
const previewImage = (index: number) => {
  uni.previewImage({
    urls: article.value.images,
    current: index
  });
};

// 导航到相关文章
const navigateToRelated = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/guide/detail?id=${id}`
  });
};

// 返回
const goBack = () => {
  uni.navigateBack();
};

// 页面加载
onLoad((options: any) => {
  if (options.id) {
    article.value.id = options.id;
    // 这里应该根据ID加载具体文章内容
  }
});
</script>

<style lang="scss" scoped>
.guide-detail-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.content-scroll {
  flex: 1;
  height: 0;
}

.author-avatar {
  width: 60rpx;
  height: 60rpx;
}

.tag-item {
  padding: 8rpx 16rpx;
  background-color: #f0f0f0;
  color: #666;
  border-radius: 16rpx;
  font-size: 22rpx;
}

.article-image {
  width: 100%;
  border-radius: 12rpx;
}

.interaction-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  
  .btn-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
    
    &.liked {
      background-color: #ff6d00;
      color: white;
    }
    
    &.collected {
      background-color: #ff6d00;
      color: white;
    }
  }
  
  .btn-text {
    color: #666;
  }
}

.related-image {
  width: 120rpx;
  height: 90rpx;
}

.related-item {
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.98);
  }
}

.border-bottom {
  border-bottom: 1px solid #f0f0f0;
}

.color-grey {
  color: #666;
}

.line-height-1-4 {
  line-height: 1.4;
}

.line-height-1-6 {
  line-height: 1.6;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
