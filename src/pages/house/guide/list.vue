<template>
  <view class="guide-list-page">
    <!-- 顶部导航 -->
    <view class="nav-header bg-white px-20rpx py-20rpx flex items-center shadow-sm">
      <view class="back-btn mr-20rpx" @tap="goBack">
        <text class="i-carbon-arrow-left text-32rpx color-grey"></text>
      </view>
      <text class="text-32rpx font-bold flex-1">房产知识</text>
      <view class="search-btn" @tap="navigateToSearch">
        <text class="i-carbon-search text-28rpx color-grey"></text>
      </view>
    </view>

    <!-- 分类标签 -->
    <view class="category-tabs bg-white">
      <scroll-view scroll-x class="tabs-scroll">
        <view class="tabs-container flex px-20rpx">
          <view
            v-for="(category, index) in categories"
            :key="index"
            class="tab-item mr-30rpx py-20rpx"
            :class="{ 'active': currentCategory === index }"
            @tap="switchCategory(index)"
          >
            <text class="text-28rpx">{{ category.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 文章列表 -->
    <view class="article-list">
      <z-paging
        ref="paging"
        v-model="articleList"
        @query="queryArticleList"
        :refresher-enabled="true"
        :auto-show-back-to-top="true"
        :empty-view-text="'暂无相关文章'"
      >
        <!-- 热门推荐 -->
        <template #top>
          <view v-if="currentCategory === 0 && hotArticles.length > 0" class="hot-section bg-white mb-20rpx">
            <view class="section-header px-30rpx py-20rpx border-bottom">
              <text class="text-30rpx font-bold">热门推荐</text>
            </view>
            <view class="hot-articles px-20rpx py-20rpx">
              <view
                v-for="(article, index) in hotArticles"
                :key="article.id"
                class="hot-article-item mb-20rpx"
                @tap="navigateToDetail(article.id)"
              >
                <view class="flex">
                  <image
                    :src="article.image"
                    class="article-image rounded-lg mr-20rpx"
                    mode="aspectFill"
                  ></image>
                  <view class="article-info flex-1">
                    <text class="article-title text-30rpx font-bold line-clamp-2">{{ article.title }}</text>
                    <text class="article-summary text-26rpx color-grey line-clamp-2 mt-10rpx">{{ article.summary }}</text>
                    <view class="article-meta flex justify-between items-center mt-15rpx">
                      <view class="flex items-center">
                        <text class="author text-24rpx color-grey">{{ article.author }}</text>
                        <text class="publish-time text-24rpx color-grey ml-20rpx">{{ article.publishTime }}</text>
                      </view>
                      <view class="flex items-center">
                        <text class="i-carbon-view text-20rpx color-grey mr-6rpx"></text>
                        <text class="view-count text-24rpx color-grey">{{ article.viewCount }}</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </template>

        <!-- 普通文章列表 -->
        <view class="normal-articles px-20rpx">
          <view
            v-for="(article, index) in articleList"
            :key="article.id"
            class="article-card bg-white rounded-lg mb-20rpx shadow-sm"
            @tap="navigateToDetail(article.id)"
          >
            <view class="card-content p-30rpx">
              <!-- 文章标题 -->
              <text class="article-title text-32rpx font-bold line-clamp-2 mb-15rpx">{{ article.title }}</text>
              
              <!-- 文章摘要 -->
              <text class="article-summary text-26rpx color-grey line-clamp-3 mb-20rpx">{{ article.summary }}</text>
              
              <!-- 文章图片 -->
              <view v-if="article.images && article.images.length > 0" class="article-images mb-20rpx">
                <view v-if="article.images.length === 1" class="single-image">
                  <image
                    :src="article.images[0]"
                    class="image-item rounded-lg"
                    mode="aspectFill"
                  ></image>
                </view>
                <view v-else class="multiple-images grid grid-cols-3 gap-10rpx">
                  <image
                    v-for="(img, imgIndex) in article.images.slice(0, 3)"
                    :key="imgIndex"
                    :src="img"
                    class="image-item rounded-lg"
                    mode="aspectFill"
                  ></image>
                </view>
              </view>
              
              <!-- 文章标签 -->
              <view v-if="article.tags && article.tags.length > 0" class="article-tags mb-20rpx">
                <text
                  v-for="(tag, tagIndex) in article.tags"
                  :key="tagIndex"
                  class="tag-item mr-15rpx"
                >{{ tag }}</text>
              </view>
              
              <!-- 文章元信息 -->
              <view class="article-meta flex justify-between items-center">
                <view class="flex items-center">
                  <image
                    v-if="article.authorAvatar"
                    :src="article.authorAvatar"
                    class="author-avatar rounded-full mr-10rpx"
                    mode="aspectFill"
                  ></image>
                  <text class="author text-24rpx color-grey">{{ article.author }}</text>
                  <text class="publish-time text-24rpx color-grey ml-20rpx">{{ article.publishTime }}</text>
                </view>
                <view class="flex items-center">
                  <view class="flex items-center mr-30rpx">
                    <text class="i-carbon-view text-20rpx color-grey mr-6rpx"></text>
                    <text class="view-count text-24rpx color-grey">{{ article.viewCount }}</text>
                  </view>
                  <view class="flex items-center">
                    <text class="i-carbon-thumbs-up text-20rpx color-grey mr-6rpx"></text>
                    <text class="like-count text-24rpx color-grey">{{ article.likeCount }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </z-paging>
    </view>
  </view>
</template>

<script setup lang="ts">
// 分类数据
const categories = [
  { name: "全部", id: "all" },
  { name: "购房指南", id: "buying" },
  { name: "租房攻略", id: "renting" },
  { name: "装修知识", id: "decoration" },
  { name: "投资理财", id: "investment" },
  { name: "政策解读", id: "policy" },
  { name: "市场分析", id: "market" }
];

const currentCategory = ref(0);

// 文章列表数据
const articleList = ref<any[]>([]);
const hotArticles = ref<any[]>([]);

// z-paging组件引用
const paging = ref<any>(null);

// 模拟文章数据
const generateArticles = (count: number) => {
  const articles = [];
  const titles = [
    "新手买房必看：购房流程详解",
    "2024年房贷政策最新变化",
    "如何选择合适的房屋户型",
    "租房合同签订注意事项",
    "装修预算如何合理规划",
    "房产投资的风险与机遇",
    "学区房购买指南",
    "二手房交易流程详解",
    "房屋验收标准与技巧",
    "公积金贷款申请攻略"
  ];
  
  const summaries = [
    "详细介绍购房的各个环节，帮助新手避免常见陷阱，顺利完成购房过程。",
    "解读最新的房贷政策变化，分析对购房者的影响，提供应对策略。",
    "从实用性、采光、通风等角度分析不同户型的优缺点，帮助选择最适合的房型。",
    "详细说明租房合同中需要注意的条款，保护租客合法权益。",
    "提供装修预算制定方法，避免超支，实现性价比最高的装修效果。"
  ];

  for (let i = 0; i < count; i++) {
    articles.push({
      id: `article_${i + 1}`,
      title: titles[i % titles.length],
      summary: summaries[i % summaries.length],
      author: `专家${i + 1}`,
      authorAvatar: `https://picsum.photos/seed/author${i}/100/100`,
      publishTime: `${Math.floor(Math.random() * 30) + 1}天前`,
      viewCount: `${(Math.random() * 50 + 10).toFixed(1)}万`,
      likeCount: Math.floor(Math.random() * 1000 + 100),
      images: Math.random() > 0.5 ? [
        `https://picsum.photos/seed/article${i}_1/400/300`,
        `https://picsum.photos/seed/article${i}_2/400/300`,
        `https://picsum.photos/seed/article${i}_3/400/300`
      ].slice(0, Math.floor(Math.random() * 3) + 1) : [],
      tags: ["购房指南", "实用技巧", "专业解读"].slice(0, Math.floor(Math.random() * 3) + 1),
      category: categories[Math.floor(Math.random() * categories.length)].id
    });
  }
  
  return articles;
};

// 切换分类
const switchCategory = (index: number) => {
  currentCategory.value = index;
  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 查询文章列表
const queryArticleList = (pageNo: number, pageSize: number) => {
  // 模拟API请求
  setTimeout(() => {
    const newArticles = generateArticles(pageSize);
    paging.value.complete(newArticles);
  }, 500);
};

// 导航到文章详情
const navigateToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/guide/detail?id=${id}`
  });
};

// 导航到搜索
const navigateToSearch = () => {
  uni.navigateTo({
    url: `/pages/house/guide/search`
  });
};

// 返回
const goBack = () => {
  uni.navigateBack();
};

// 页面加载
onLoad(() => {
  // 加载热门文章
  hotArticles.value = generateArticles(3);
});
</script>

<style lang="scss" scoped>
.guide-list-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs-container {
  display: inline-flex;
  min-width: 100%;
}

.tab-item {
  flex-shrink: 0;
  position: relative;
  
  &.active {
    color: $primary;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40rpx;
      height: 4rpx;
      background-color: $primary;
      border-radius: 2rpx;
    }
  }
}

.article-image {
  width: 200rpx;
  height: 140rpx;
}

.article-card {
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.98);
  }
}

.single-image .image-item {
  width: 100%;
  height: 300rpx;
}

.multiple-images .image-item {
  width: 100%;
  height: 160rpx;
}

.author-avatar {
  width: 40rpx;
  height: 40rpx;
}

.tag-item {
  padding: 6rpx 16rpx;
  background-color: #f0f0f0;
  color: #666;
  border-radius: 16rpx;
  font-size: 22rpx;
}

.border-bottom {
  border-bottom: 1px solid #f0f0f0;
}

.color-grey {
  color: #666;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
