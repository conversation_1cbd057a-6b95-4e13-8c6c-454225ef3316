<template>
  <view class="map-page">
    <!-- 顶部搜索栏 -->
    <view
      class="map-header bg-white px-20rpx py-20rpx flex items-center shadow-sm"
    >
      <view class="back-btn mr-20rpx" @tap="goBack">
        <text class="i-carbon-arrow-left text-32rpx color-grey"></text>
      </view>
      <view
        class="search-box flex-1 flex items-center bg-gray-50 rounded-full px-24rpx py-16rpx"
        @tap="navigateToSearch"
      >
        <text class="i-carbon-search text-26rpx color-grey mr-12rpx"></text>
        <text class="color-grey text-26rpx">搜索位置</text>
      </view>
      <view class="filter-btn ml-20rpx" @tap="showFilterPanel">
        <text class="i-carbon-filter text-28rpx color-grey"></text>
      </view>
    </view>

    <!-- 地图容器 -->
    <view class="map-container">
      <map
        id="houseMap"
        class="map"
        :longitude="mapCenter.longitude"
        :latitude="mapCenter.latitude"
        :scale="mapScale"
        :markers="mapMarkers"
        :show-location="true"
        @markertap="onMarkerTap"
        @regionchange="onRegionChange"
      ></map>

      <!-- 地图控制按钮 -->
      <view class="map-controls">
        <!-- 定位按钮 -->
        <view class="control-btn location-btn" @tap="getCurrentLocation">
          <text class="i-carbon-location text-32rpx color-primary"></text>
        </view>

        <!-- 缩放按钮 -->
        <view class="control-btn zoom-in-btn" @tap="zoomIn">
          <text class="i-carbon-add text-32rpx color-grey"></text>
        </view>
        <view class="control-btn zoom-out-btn" @tap="zoomOut">
          <text class="i-carbon-subtract text-32rpx color-grey"></text>
        </view>
      </view>
    </view>

    <!-- 底部房源信息面板 -->
    <view
      class="bottom-panel bg-white"
      :class="{ 'panel-expanded': showHouseDetail }"
    >
      <!-- 房源统计 -->
      <view class="panel-header px-30rpx py-20rpx border-bottom">
        <view class="flex justify-between items-center">
          <text class="text-28rpx color-main"
            >当前区域共 {{ visibleHouses.length }} 套房源</text
          >
          <view class="flex items-center" @tap="toggleListView">
            <text class="text-26rpx color-grey mr-10rpx">列表查看</text>
            <text class="i-carbon-list text-24rpx color-grey"></text>
          </view>
        </view>
      </view>

      <!-- 房源详情 -->
      <view v-if="selectedHouse" class="house-detail px-30rpx py-20rpx">
        <view class="flex">
          <image
            :src="selectedHouse.image"
            class="house-image rounded-lg mr-20rpx"
            mode="aspectFill"
          ></image>
          <view class="house-info flex-1">
            <text class="text-30rpx font-bold line-clamp-1">{{
              selectedHouse.title
            }}</text>
            <text class="text-26rpx color-grey mt-10rpx">{{
              selectedHouse.info
            }}</text>
            <view class="flex flex-wrap mt-10rpx">
              <text
                v-for="(tag, index) in selectedHouse.tags"
                :key="index"
                class="tag-item mr-10rpx mb-10rpx"
                >{{ tag }}</text
              >
            </view>
            <view class="flex justify-between items-center mt-10rpx">
              <text class="text-32rpx color-red font-bold">{{
                selectedHouse.price
              }}</text>
              <button
                class="detail-btn bg-primary text-white text-26rpx px-24rpx py-8rpx rounded-full"
                @tap="viewHouseDetail"
              >
                查看详情
              </button>
            </view>
          </view>
        </view>
      </view>

      <!-- 房源列表预览 -->
      <view v-else class="house-preview">
        <scroll-view scroll-x class="preview-scroll">
          <view class="preview-list flex px-20rpx">
            <view
              v-for="house in visibleHouses.slice(0, 10)"
              :key="house.id"
              class="preview-item mr-20rpx"
              @tap="selectHouse(house)"
            >
              <image
                :src="house.image"
                class="preview-image rounded-lg"
                mode="aspectFill"
              ></image>
              <text
                class="preview-price text-24rpx color-red font-bold mt-8rpx"
                >{{ house.price }}</text
              >
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 筛选面板 -->
    <view v-if="showFilter" class="filter-overlay" @tap="hideFilterPanel">
      <view class="filter-panel bg-white" @tap.stop>
        <view class="filter-header px-30rpx py-20rpx border-bottom">
          <view class="flex justify-between items-center">
            <text class="text-32rpx font-bold">筛选条件</text>
            <view @tap="hideFilterPanel">
              <text class="i-carbon-close text-28rpx color-grey"></text>
            </view>
          </view>
        </view>

        <view class="filter-content px-30rpx py-30rpx">
          <!-- 价格范围 -->
          <view class="filter-section mb-40rpx">
            <text class="section-title text-28rpx font-bold mb-20rpx block"
              >价格范围</text
            >
            <view class="flex flex-wrap">
              <view
                v-for="(price, index) in priceRanges"
                :key="index"
                class="filter-tag mr-20rpx mb-20rpx"
                :class="{ active: selectedPriceRange === price }"
                @tap="selectPriceRange(price)"
              >
                <text class="text-26rpx">{{ price }}</text>
              </view>
            </view>
          </view>

          <!-- 房源类型 -->
          <view class="filter-section mb-40rpx">
            <text class="section-title text-28rpx font-bold mb-20rpx block"
              >房源类型</text
            >
            <view class="flex flex-wrap">
              <view
                v-for="(type, index) in houseTypes"
                :key="index"
                class="filter-tag mr-20rpx mb-20rpx"
                :class="{ active: selectedHouseType === type }"
                @tap="selectHouseType(type)"
              >
                <text class="text-26rpx">{{ type }}</text>
              </view>
            </view>
          </view>
        </view>

        <view class="filter-footer px-30rpx py-20rpx border-top">
          <view class="flex">
            <button class="reset-btn flex-1 mr-20rpx" @tap="resetFilter">
              重置
            </button>
            <button
              class="confirm-btn flex-1 bg-primary text-white"
              @tap="applyFilter"
            >
              确定
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { generateMoreHouses } from "@/utils/data/houseListData";
import type { HouseItemType } from "@/types/house";

// 地图相关
const mapCenter = ref({
  longitude: 116.397428,
  latitude: 39.90923,
});
const mapScale = ref(16);

// 房源数据
const allHouses = ref<HouseItemType[]>([]);
const visibleHouses = ref<HouseItemType[]>([]);
const selectedHouse = ref<HouseItemType>(null);
const showHouseDetail = ref(false);

// 地图标记
const mapMarkers = ref<any[]>([]);

// 筛选相关
const showFilter = ref(false);
const selectedPriceRange = ref("");
const selectedHouseType = ref("");

const priceRanges = ["不限", "50万以下", "50-100万", "100-200万", "200万以上"];
const houseTypes = ["不限", "一室", "二室", "三室", "四室及以上"];

// 初始化数据
const initData = () => {
  allHouses.value = generateMoreHouses(20);
  visibleHouses.value = allHouses.value;
  updateMapMarkers();
};

// 更新地图标记
const updateMapMarkers = () => {
  mapMarkers.value = visibleHouses.value.map((house, index) => ({
    id: house.id,
    latitude: mapCenter.value.latitude + (Math.random() - 0.5) * 0.01,
    longitude: mapCenter.value.longitude + (Math.random() - 0.5) * 0.01,
    iconPath: "/static/icons/house-marker.png",
    width: 40,
    height: 40,
    callout: {
      content: house.price,
      color: "#ffffff",
      fontSize: 12,
      borderRadius: 4,
      bgColor: "#ff6d00",
      padding: 4,
      display: "ALWAYS",
    },
  }));
};

// 地图事件处理
const onMarkerTap = (e: any) => {
  const markerId = e.detail.markerId;
  const house = allHouses.value.find((h) => h.id === markerId);
  if (house) {
    selectHouse(house);
  }
};

const onRegionChange = (e: any) => {
  if (e.detail.type === "end") {
    // 地图移动结束，可以重新加载该区域的房源
    console.log("地图区域变化", e.detail);
  }
};

// 选择房源
const selectHouse = (house: HouseItemType) => {
  selectedHouse.value = house;
  showHouseDetail.value = true;
};

// 获取当前位置
const getCurrentLocation = () => {
  uni.getLocation({
    type: "gcj02",
    success: (res) => {
      mapCenter.value = {
        longitude: res.longitude,
        latitude: res.latitude,
      };
    },
    fail: () => {
      uni.showToast({
        title: "获取位置失败",
        icon: "none",
      });
    },
  });
};

// 地图缩放
const zoomIn = () => {
  if (mapScale.value < 20) {
    mapScale.value += 1;
  }
};

const zoomOut = () => {
  if (mapScale.value > 5) {
    mapScale.value -= 1;
  }
};

// 筛选相关方法
const showFilterPanel = () => {
  showFilter.value = true;
};

const hideFilterPanel = () => {
  showFilter.value = false;
};

const selectPriceRange = (price: string) => {
  selectedPriceRange.value = price;
};

const selectHouseType = (type: string) => {
  selectedHouseType.value = type;
};

const resetFilter = () => {
  selectedPriceRange.value = "";
  selectedHouseType.value = "";
};

const applyFilter = () => {
  // 应用筛选条件
  let filtered = allHouses.value;

  // 这里应该根据实际筛选条件过滤数据
  visibleHouses.value = filtered;
  updateMapMarkers();
  hideFilterPanel();
};

// 其他方法
const toggleListView = () => {
  uni.navigateTo({
    url: "/pages/house/list",
  });
};

const viewHouseDetail = () => {
  if (selectedHouse.value) {
    uni.navigateTo({
      url: `/pages/house/detail?id=${selectedHouse.value.id}`,
    });
  }
};

const navigateToSearch = () => {
  uni.navigateTo({
    url: "/pages/house/search",
  });
};

const goBack = () => {
  uni.navigateBack();
};

// 页面加载
onLoad(() => {
  initData();
});
</script>

<style lang="scss" scoped>
.map-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.map-container {
  flex: 1;
  position: relative;
}

.map {
  width: 100%;
  height: 100%;
}

.map-controls {
  position: absolute;
  right: 20rpx;
  bottom: 200rpx;
  display: flex;
  flex-direction: column;
}

.control-btn {
  width: 80rpx;
  height: 80rpx;
  background-color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.bottom-panel {
  max-height: 400rpx;
  transition: all 0.3s ease;
}

.panel-expanded {
  max-height: 600rpx;
}

.house-image {
  width: 160rpx;
  height: 120rpx;
}

.preview-scroll {
  white-space: nowrap;
}

.preview-list {
  display: inline-flex;
  padding: 20rpx 0;
}

.preview-item {
  width: 120rpx;
  flex-shrink: 0;
}

.preview-image {
  width: 120rpx;
  height: 90rpx;
}

.filter-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.filter-panel {
  width: 100%;
  max-height: 80vh;
  border-radius: 20rpx 20rpx 0 0;
}

.filter-tag {
  padding: 12rpx 24rpx;
  border: 1px solid #e5e7eb;
  border-radius: 20rpx;

  &.active {
    background-color: $primary;
    border-color: $primary;
    color: white;
  }
}

.border-bottom {
  border-bottom: 1px solid #f0f0f0;
}

.border-top {
  border-top: 1px solid #f0f0f0;
}

.tag-item {
  padding: 4rpx 12rpx;
  background-color: #f6f6f6;
  color: #666;
  border-radius: 4rpx;
  font-size: 22rpx;
}

.color-main {
  color: #333;
}

.color-grey {
  color: #666;
}

.color-red {
  color: #fa5741;
}

.reset-btn,
.confirm-btn {
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.reset-btn {
  background-color: #f5f5f5;
  color: #666;
}
</style>
