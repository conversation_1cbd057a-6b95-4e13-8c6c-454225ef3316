<template>
  <view class="valuation-page">
    <!-- 顶部导航 -->
    <view class="nav-header bg-white px-20rpx py-20rpx flex items-center shadow-sm">
      <view class="back-btn mr-20rpx" @tap="goBack">
        <text class="i-carbon-arrow-left text-32rpx color-grey"></text>
      </view>
      <text class="text-32rpx font-bold flex-1">房价评估</text>
    </view>

    <!-- 功能介绍 -->
    <view class="intro-section bg-white mx-20rpx mt-20rpx mb-20rpx rounded-xl shadow-sm">
      <view class="px-30rpx py-30rpx">
        <view class="flex items-center mb-20rpx">
          <view class="intro-icon bg-primary rounded-full mr-15rpx">
            <text class="i-carbon-analytics text-white text-32rpx"></text>
          </view>
          <text class="text-30rpx font-bold">智能房价评估</text>
        </view>
        <text class="text-26rpx color-grey line-height-1-6">
          基于大数据分析，为您提供准确的房价评估服务。输入房屋信息，即可获得专业的价格评估报告。
        </text>
      </view>
    </view>

    <!-- 房屋信息输入 -->
    <view class="input-section bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm">
      <view class="section-header px-30rpx py-20rpx border-bottom">
        <text class="text-30rpx font-bold">房屋信息</text>
      </view>
      
      <view class="input-list">
        <!-- 房屋地址 -->
        <view class="input-item px-30rpx py-30rpx border-bottom">
          <view class="flex justify-between items-center">
            <text class="text-28rpx">房屋地址</text>
            <view class="input-container flex-1 ml-30rpx" @tap="selectAddress">
              <input
                v-model="houseInfo.address"
                class="text-input text-right"
                placeholder="请选择小区地址"
                disabled
              />
              <text class="i-carbon-chevron-right text-20rpx color-grey ml-10rpx"></text>
            </view>
          </view>
        </view>

        <!-- 房屋面积 -->
        <view class="input-item px-30rpx py-30rpx border-bottom">
          <view class="flex justify-between items-center">
            <text class="text-28rpx">建筑面积</text>
            <view class="input-container">
              <input
                v-model="houseInfo.area"
                type="digit"
                class="text-input text-right"
                placeholder="请输入面积"
              />
              <text class="text-26rpx color-grey ml-10rpx">㎡</text>
            </view>
          </view>
        </view>

        <!-- 房屋户型 -->
        <view class="input-item px-30rpx py-30rpx border-bottom">
          <view class="flex justify-between items-center">
            <text class="text-28rpx">房屋户型</text>
            <picker
              mode="selector"
              :range="houseTypes"
              :value="houseInfo.typeIndex"
              @change="onTypeChange"
            >
              <view class="picker-text">
                <text class="text-28rpx color-primary">{{ houseTypes[houseInfo.typeIndex] }}</text>
                <text class="i-carbon-chevron-down text-20rpx color-grey ml-10rpx"></text>
              </view>
            </picker>
          </view>
        </view>

        <!-- 房屋楼层 -->
        <view class="input-item px-30rpx py-30rpx border-bottom">
          <view class="flex justify-between items-center">
            <text class="text-28rpx">所在楼层</text>
            <view class="input-container">
              <input
                v-model="houseInfo.floor"
                type="number"
                class="text-input text-right"
                placeholder="如：15"
              />
              <text class="text-26rpx color-grey ml-10rpx">层</text>
            </view>
          </view>
        </view>

        <!-- 总楼层 -->
        <view class="input-item px-30rpx py-30rpx border-bottom">
          <view class="flex justify-between items-center">
            <text class="text-28rpx">总楼层</text>
            <view class="input-container">
              <input
                v-model="houseInfo.totalFloor"
                type="number"
                class="text-input text-right"
                placeholder="如：32"
              />
              <text class="text-26rpx color-grey ml-10rpx">层</text>
            </view>
          </view>
        </view>

        <!-- 装修情况 -->
        <view class="input-item px-30rpx py-30rpx">
          <view class="flex justify-between items-center">
            <text class="text-28rpx">装修情况</text>
            <picker
              mode="selector"
              :range="decorationTypes"
              :value="houseInfo.decorationIndex"
              @change="onDecorationChange"
            >
              <view class="picker-text">
                <text class="text-28rpx color-primary">{{ decorationTypes[houseInfo.decorationIndex] }}</text>
                <text class="i-carbon-chevron-down text-20rpx color-grey ml-10rpx"></text>
              </view>
            </picker>
          </view>
        </view>
      </view>
    </view>

    <!-- 评估按钮 -->
    <view class="eval-btn-container px-30rpx mb-30rpx">
      <button class="eval-btn bg-primary text-white" @tap="startEvaluation">
        开始评估
      </button>
    </view>

    <!-- 评估结果 -->
    <view v-if="showResult" class="result-section bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm">
      <view class="section-header px-30rpx py-20rpx border-bottom">
        <text class="text-30rpx font-bold">评估结果</text>
      </view>
      
      <view class="result-content px-30rpx py-30rpx">
        <!-- 估价结果 -->
        <view class="price-result text-center mb-40rpx">
          <text class="result-label text-26rpx color-grey">房屋估价</text>
          <text class="result-price text-48rpx font-bold color-primary block mt-10rpx">{{ result.totalPrice }}万元</text>
          <text class="result-unit-price text-28rpx color-grey mt-10rpx">约{{ result.unitPrice }}元/㎡</text>
        </view>

        <!-- 价格区间 -->
        <view class="price-range mb-40rpx">
          <text class="range-label text-26rpx color-grey">价格区间</text>
          <view class="range-bar mt-20rpx">
            <view class="range-track bg-gray-200 rounded-full relative">
              <view class="range-fill bg-primary rounded-full"></view>
              <view class="range-point bg-primary rounded-full"></view>
            </view>
            <view class="range-labels flex justify-between mt-15rpx">
              <text class="text-24rpx color-grey">{{ result.minPrice }}万</text>
              <text class="text-24rpx color-grey">{{ result.maxPrice }}万</text>
            </view>
          </view>
        </view>

        <!-- 评估详情 -->
        <view class="eval-details">
          <text class="details-title text-28rpx font-bold mb-20rpx block">评估依据</text>
          <view class="detail-item flex justify-between py-15rpx border-bottom">
            <text class="text-26rpx">小区均价</text>
            <text class="text-26rpx font-bold">{{ result.communityPrice }}元/㎡</text>
          </view>
          <view class="detail-item flex justify-between py-15rpx border-bottom">
            <text class="text-26rpx">楼层系数</text>
            <text class="text-26rpx font-bold">{{ result.floorFactor }}</text>
          </view>
          <view class="detail-item flex justify-between py-15rpx border-bottom">
            <text class="text-26rpx">装修系数</text>
            <text class="text-26rpx font-bold">{{ result.decorationFactor }}</text>
          </view>
          <view class="detail-item flex justify-between py-15rpx">
            <text class="text-26rpx">市场热度</text>
            <text class="text-26rpx font-bold">{{ result.marketHeat }}</text>
          </view>
        </view>

        <!-- 免责声明 -->
        <view class="disclaimer mt-40rpx p-20rpx bg-gray-50 rounded-lg">
          <text class="text-24rpx color-grey line-height-1-6">
            * 此评估结果仅供参考，实际成交价格可能因市场变化、房屋具体情况等因素有所差异。
          </text>
        </view>
      </view>
    </view>

    <!-- 功能即将上线提示 -->
    <view v-if="!showResult" class="coming-soon bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm">
      <view class="text-center py-80rpx">
        <text class="i-carbon-construction text-80rpx color-grey block mb-20rpx"></text>
        <text class="text-28rpx color-grey">房价评估功能</text>
        <text class="text-24rpx color-grey block mt-10rpx">正在完善中，敬请期待</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 房屋信息
const houseInfo = reactive({
  address: "",
  area: "",
  typeIndex: 0,
  floor: "",
  totalFloor: "",
  decorationIndex: 0
});

// 房屋户型选项
const houseTypes = ["一室", "二室", "三室", "四室", "五室及以上"];

// 装修情况选项
const decorationTypes = ["毛坯", "简装", "精装", "豪装"];

// 评估结果
const result = reactive({
  totalPrice: "0",
  unitPrice: "0",
  minPrice: "0",
  maxPrice: "0",
  communityPrice: "0",
  floorFactor: "0",
  decorationFactor: "0",
  marketHeat: "0"
});

const showResult = ref(false);

// 选择地址
const selectAddress = () => {
  uni.showActionSheet({
    itemList: ["阳光花园", "金桥国际", "碧桂园", "万达广场", "其他小区"],
    success: (res) => {
      const addresses = ["阳光花园", "金桥国际", "碧桂园", "万达广场", "其他小区"];
      houseInfo.address = addresses[res.tapIndex];
    }
  });
};

// 选择户型
const onTypeChange = (e: any) => {
  houseInfo.typeIndex = e.detail.value;
};

// 选择装修情况
const onDecorationChange = (e: any) => {
  houseInfo.decorationIndex = e.detail.value;
};

// 开始评估
const startEvaluation = () => {
  if (!houseInfo.address || !houseInfo.area) {
    uni.showToast({
      title: "请填写完整信息",
      icon: "none"
    });
    return;
  }

  // 模拟评估计算
  const area = parseFloat(houseInfo.area);
  const basePrice = 8000; // 基础单价
  
  // 楼层系数
  const floor = parseInt(houseInfo.floor) || 1;
  const totalFloor = parseInt(houseInfo.totalFloor) || 30;
  let floorFactor = 1.0;
  if (floor <= 3) floorFactor = 0.95;
  else if (floor >= totalFloor - 2) floorFactor = 0.98;
  else floorFactor = 1.02;

  // 装修系数
  const decorationFactors = [0.95, 1.0, 1.1, 1.2];
  const decorationFactor = decorationFactors[houseInfo.decorationIndex];

  // 计算估价
  const unitPrice = Math.round(basePrice * floorFactor * decorationFactor);
  const totalPrice = Math.round((unitPrice * area) / 10000 * 100) / 100;

  // 更新结果
  result.totalPrice = totalPrice.toFixed(2);
  result.unitPrice = unitPrice.toString();
  result.minPrice = (totalPrice * 0.9).toFixed(2);
  result.maxPrice = (totalPrice * 1.1).toFixed(2);
  result.communityPrice = basePrice.toString();
  result.floorFactor = floorFactor.toFixed(2);
  result.decorationFactor = decorationFactor.toFixed(2);
  result.marketHeat = "较热";

  showResult.value = true;
};

// 返回
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.valuation-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.intro-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.input-container {
  display: flex;
  align-items: center;
}

.text-input {
  font-size: 28rpx;
  color: #333;
  min-width: 200rpx;
}

.picker-text {
  display: flex;
  align-items: center;
}

.eval-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.range-track {
  height: 8rpx;
  position: relative;
}

.range-fill {
  width: 60%;
  height: 100%;
}

.range-point {
  position: absolute;
  top: -6rpx;
  left: 60%;
  width: 20rpx;
  height: 20rpx;
  transform: translateX(-50%);
}

.border-bottom {
  border-bottom: 1px solid #f0f0f0;
}

.color-grey {
  color: #666;
}

.color-primary {
  color: $primary;
}

.line-height-1-6 {
  line-height: 1.6;
}
</style>
