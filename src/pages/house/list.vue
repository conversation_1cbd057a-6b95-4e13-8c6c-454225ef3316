<template>
  <view class="house-list-container">
    <!-- 顶部搜索栏 -->
    <view class="search-bar bg-white px-30rpx py-20rpx flex items-center">
      <view
        class="search-input flex-1 flex items-center bg-gray-100 rounded-full px-20rpx py-14rpx"
      >
        <text class="i-carbon-search mr-10rpx color-grey"></text>
        <input
          type="text"
          class="flex-1 text-28rpx"
          placeholder="输入小区名称、地点"
          v-model="keyword"
          confirm-type="search"
          @confirm="handleSearch"
        />
      </view>
      <view class="map-btn ml-20rpx" @tap="navigateToMap">
        <text class="i-carbon-map text-40rpx color-info"></text>
      </view>
    </view>

    <!-- 筛选栏 -->
    <view class="filter-bar sticky-top">
      <house-filter
        :activeFilters="activeFilters"
        @filter-change="handleFilterChange"
      ></house-filter>
    </view>

    <!-- 房源列表 -->
    <z-paging
      ref="paging"
      v-model="houseList"
      @query="queryHouseList"
      :default-page-size="10"
      use-virtual-list
      :refresher-enabled="true"
      :show-refresher-when-reload="true"
      :auto-show-back-to-top="true"
      :empty-view-text="'暂无房源信息'"
      loading-more-custom-style="font-size: 24rpx;"
    >
      <template #top>
        <!-- 可选：热门房源推荐区域 -->
        <featured-houses
          v-if="showFeatured && featuredHouses.length > 0"
        ></featured-houses>
      </template>

      <view class="house-list px-20rpx">
        <house-item
          v-for="(item, index) in houseList"
          :key="item.id"
          :house="item"
          class="mb-20rpx"
          @tap="navigateToDetail(item.id)"
        ></house-item>
      </view>

      <template #empty>
        <empty-result
          message="没有找到符合条件的房源"
          sub-message="请尝试调整筛选条件"
          icon="i-carbon-no-image"
        ></empty-result>
      </template>
    </z-paging>

    <!-- 返回顶部按钮，由z-paging内部控制 -->

    <!-- 底部占位，防止内容被遮挡 -->
    <view class="h-20rpx"></view>
  </view>
</template>

<script setup lang="ts">
import type { HouseItemType, HouseFilterType } from "@/types/house";

import HouseFilter from "@/components/house/HouseFilter.vue";
import HouseItem from "@/components/house/HouseItem.vue";
import FeaturedHouses from "@/components/house/FeaturedHouses.vue";
import EmptyResult from "@/components/common/EmptyResult.vue";
import { generateMoreHouses } from "@/utils/data/houseListData";

// 房源列表数据
const houseList = ref<HouseItemType[]>([]);
const featuredHouses = ref<HouseFilterType[]>([]);
const showFeatured = ref(true);
const keyword = ref("");

// z-paging组件引用
const paging = ref<any>(null);

// 筛选条件
const activeFilters = reactive<HouseFilterType>({
  area: undefined,
  rentType: undefined,
  priceRange: undefined,
  houseType: undefined,
  sortBy: "默认排序",
});

// 页面激活时刷新数据
onLoad(() => {
  paging.value?.reload();
});

// 查询房源列表（模拟接口请求）
const queryHouseList = async (pageNo: number, pageSize: number) => {
  try {
    // 模拟API请求延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 模拟数据生成
    const list = generateMoreHouses(pageSize);

    // 模拟数据总数
    const total = pageNo === 1 ? 58 : 0;

    // 第一页加载特色房源
    if (pageNo === 1) {
      // 模拟特色房源数据
      featuredHouses.value = generateMoreHouses(3);
    }

    // 返回数据给z-paging组件
    paging.value?.complete(list, total);
  } catch (error) {
    console.error("加载房源列表失败", error);
    paging.value?.complete(false);
    uni.showToast({
      title: "加载失败，请重试",
      icon: "none",
    });
  }
};

// 处理筛选条件变化
const handleFilterChange = (filter: HouseFilterType) => {
  Object.assign(activeFilters, filter);
  paging.value?.reload();
};

// 处理搜索
const handleSearch = () => {
  activeFilters.keyword = keyword.value;
  paging.value?.reload();
};

// 跳转到地图找房页面
const navigateToMap = () => {
  uni.navigateTo({
    url: "/pages/house/map",
  });
};

// 跳转到房源详情页
const navigateToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/detail?id=${id}`,
  });
};
</script>

<style lang="scss" scoped>
.house-list-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.search-bar {
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-input {
  height: 72rpx;
}

.filter-bar {
  position: sticky;
  top: 112rpx; // 搜索栏高度
  z-index: 99;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 99;
}
</style>
