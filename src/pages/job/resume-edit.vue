<template>
  <view class="edit-container">
    <!-- 导航栏 -->
    <view class="nav-bar-wrapper">
      <uni-nav-bar
        :fixed="true"
        :border="false"
        status-bar
        :title="getPageTitle"
        left-icon="left"
        background-color="transparent"
        @clickLeft="goBack"
      />
    </view>

    <!-- 编辑表单 -->
    <scroll-view scroll-y class="edit-content">
      <!-- 个人信息编辑 -->
      <view v-if="section === 'personal'" class="edit-form">
        <view class="form-group">
          <view class="form-label">姓名</view>
          <input
            class="form-input"
            type="text"
            v-model="formData.name"
            placeholder="请输入姓名"
          />
        </view>

        <view class="form-group">
          <view class="form-label">性别</view>
          <view class="radio-group">
            <view
              class="radio-item"
              :class="{ active: formData.gender === '男' }"
              @tap="formData.gender = '男'"
            >
              <text
                class="radio-icon i-carbon-radio-button"
                v-if="formData.gender !== '男'"
              ></text>
              <text
                class="radio-icon i-carbon-radio-button-checked"
                v-else
              ></text>
              <text>男</text>
            </view>
            <view
              class="radio-item"
              :class="{ active: formData.gender === '女' }"
              @tap="formData.gender = '女'"
            >
              <text
                class="radio-icon i-carbon-radio-button"
                v-if="formData.gender !== '女'"
              ></text>
              <text
                class="radio-icon i-carbon-radio-button-checked"
                v-else
              ></text>
              <text>女</text>
            </view>
          </view>
        </view>

        <view class="form-group">
          <view class="form-label">年龄</view>
          <input
            class="form-input"
            type="number"
            v-model="formData.age"
            placeholder="请输入年龄"
          />
        </view>

        <view class="form-group">
          <view class="form-label">手机号码</view>
          <input
            class="form-input"
            type="number"
            v-model="formData.phone"
            placeholder="请输入手机号码"
            maxlength="11"
          />
        </view>

        <view class="form-group">
          <view class="form-label">邮箱</view>
          <input
            class="form-input"
            type="text"
            v-model="formData.email"
            placeholder="请输入邮箱"
          />
        </view>

        <view class="form-group">
          <view class="form-label">求职意向</view>
          <input
            class="form-input"
            type="text"
            v-model="formData.jobIntent"
            placeholder="请输入求职意向"
          />
        </view>

        <view class="form-group">
          <view class="form-label">工作经验</view>
          <picker
            mode="selector"
            :range="experienceOptions"
            @change="onExperienceChange"
          >
            <view class="picker-view">
              <text>{{ formData.experience || "请选择工作经验" }}</text>
              <text class="i-carbon-chevron-down"></text>
            </view>
          </picker>
        </view>

        <view class="form-group">
          <view class="form-label">最高学历</view>
          <picker
            mode="selector"
            :range="educationOptions"
            @change="onEducationChange"
          >
            <view class="picker-view">
              <text>{{ formData.education || "请选择最高学历" }}</text>
              <text class="i-carbon-chevron-down"></text>
            </view>
          </picker>
        </view>

        <view class="form-group">
          <view class="form-label">期望薪资</view>
          <picker
            mode="selector"
            :range="salaryOptions"
            @change="onSalaryChange"
          >
            <view class="picker-view">
              <text>{{ formData.salary || "请选择期望薪资" }}</text>
              <text class="i-carbon-chevron-down"></text>
            </view>
          </picker>
        </view>
      </view>

      <!-- 教育经历编辑 -->
      <view v-else-if="section === 'education'" class="edit-form">
        <view class="form-group">
          <view class="form-label">学校名称</view>
          <input
            class="form-input"
            type="text"
            v-model="formData.school"
            placeholder="请输入学校名称"
          />
        </view>

        <view class="form-group">
          <view class="form-label">专业</view>
          <input
            class="form-input"
            type="text"
            v-model="formData.major"
            placeholder="请输入专业名称"
          />
        </view>

        <view class="form-group">
          <view class="form-label">学历</view>
          <picker
            mode="selector"
            :range="educationOptions"
            @change="onDegreeChange"
          >
            <view class="picker-view">
              <text>{{ formData.degree || "请选择学历" }}</text>
              <text class="i-carbon-chevron-down"></text>
            </view>
          </picker>
        </view>

        <view class="form-group">
          <view class="form-label">起止时间</view>
          <input
            class="form-input"
            type="text"
            v-model="formData.time"
            placeholder="例如：2016.09-2020.07"
          />
        </view>
      </view>

      <!-- 工作经验编辑 -->
      <view v-else-if="section === 'experience'" class="edit-form">
        <view class="form-group">
          <view class="form-label">公司名称</view>
          <input
            class="form-input"
            type="text"
            v-model="formData.company"
            placeholder="请输入公司名称"
          />
        </view>

        <view class="form-group">
          <view class="form-label">职位名称</view>
          <input
            class="form-input"
            type="text"
            v-model="formData.title"
            placeholder="请输入职位名称"
          />
        </view>

        <view class="form-group">
          <view class="form-label">起止时间</view>
          <input
            class="form-input"
            type="text"
            v-model="formData.time"
            placeholder="例如：2020.07-至今"
          />
        </view>

        <view class="form-group">
          <view class="form-label">工作内容</view>
          <view class="work-items">
            <view
              v-for="(item, index) in formData.responsibilities"
              :key="index"
              class="work-item-edit"
            >
              <textarea
                class="form-textarea"
                v-model="formData.responsibilities[index]"
                placeholder="请输入工作内容"
              />
              <view class="delete-btn" @tap="deleteWorkItem(index)">
                <text class="i-carbon-trash-can"></text>
              </view>
            </view>
            <view class="add-work-item" @tap="addWorkItem">
              <text class="i-carbon-add"></text>
              <text>添加工作内容</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 技能编辑 -->
      <view v-else-if="section === 'skills'" class="edit-form">
        <view class="form-group">
          <view class="form-label">技能标签</view>
          <view class="skills-edit">
            <view
              v-for="(skill, index) in formData.skills"
              :key="index"
              class="skill-tag-edit"
            >
              <text>{{ skill }}</text>
              <text class="delete-icon" @tap="deleteSkill(index)">×</text>
            </view>
            <view class="add-skill" @tap="showAddSkill = true">
              <text class="i-carbon-add"></text>
              <text>添加技能</text>
            </view>
          </view>
        </view>

        <view class="form-group" v-if="showAddSkill">
          <input
            class="form-input"
            type="text"
            v-model="newSkill"
            placeholder="请输入技能名称"
          />
          <view class="skill-actions">
            <view class="cancel-btn" @tap="showAddSkill = false">取消</view>
            <view class="confirm-btn" @tap="addSkill">确认</view>
          </view>
        </view>

        <view class="form-group">
          <view class="form-label">技能描述</view>
          <textarea
            class="form-textarea"
            v-model="formData.skillDescription"
            placeholder="请描述你的技能和专长"
          />
        </view>
      </view>

      <!-- 自我评价编辑 -->
      <view v-else-if="section === 'evaluation'" class="edit-form">
        <view class="form-group">
          <view class="form-label">自我评价</view>
          <textarea
            class="form-textarea"
            v-model="formData.selfEvaluation"
            placeholder="请输入自我评价"
          />
        </view>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <view class="action-btn outline-btn" @tap="goBack">
        <text>取消</text>
      </view>
      <view class="action-btn primary-btn" @tap="saveData">
        <text>保存</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";

// 获取页面参数
const section = ref("");
const action = ref("");
const index = ref(-1);

// 表单数据
const formData = reactive({
  name: "",
  gender: "",
  age: "",
  phone: "",
  email: "",
  jobIntent: "",
  experience: "",
  education: "",
  salary: "",
  educations: [],
  experiences: [],
  skills: [],
  skillDescription: "",
  selfEvaluation: "",
  responsibilities: [],
  school: "",
  major: "",
  degree: "",
  time: "",
  company: "",
  title: "",
});
const showAddSkill = ref(false);
const newSkill = ref("");

// 选项数据
const experienceOptions = [
  "应届毕业生",
  "1年以下",
  "1-3年",
  "3-5年",
  "5-10年",
  "10年以上",
];
const educationOptions = ["高中", "大专", "本科", "硕士", "博士"];
const salaryOptions = [
  "面议",
  "3K以下",
  "3-5K",
  "5-8K",
  "8-10K",
  "10-15K",
  "15-20K",
  "20-30K",
  "30K以上",
];

// 页面标题
const getPageTitle = computed(() => {
  if (section.value === "personal") return "编辑个人信息";
  if (section.value === "education") {
    return action.value === "add" ? "添加教育经历" : "编辑教育经历";
  }
  if (section.value === "experience") {
    return action.value === "add" ? "添加工作经验" : "编辑工作经验";
  }
  if (section.value === "skills") return "编辑专业技能";
  if (section.value === "evaluation") return "编辑自我评价";
  return "编辑简历";
});

// 加载页面参数
onLoad((options) => {
  if (options.section) {
    section.value = options.section;
  }
  if (options.action) {
    action.value = options.action;
  }
  if (options.index) {
    index.value = parseInt(options.index);
  }

  // 初始化表单数据
  initFormData();
});

// 初始化表单数据
const initFormData = () => {
  // 这里应该从全局状态或本地存储获取简历数据
  // 为了演示，使用模拟数据
  const resumeData = {
    name: "张小明",
    gender: "男",
    age: 28,
    phone: "13812345678",
    email: "<EMAIL>",
    jobIntent: "前端开发工程师",
    experience: "3年",
    education: "本科",
    salary: "15-20K",
    educations: [
      {
        school: "浙江大学",
        major: "计算机科学与技术",
        degree: "本科",
        time: "2016.09-2020.07",
      },
    ],
    experiences: [
      {
        company: "杭州某科技有限公司",
        title: "前端开发工程师",
        time: "2020.07-至今",
        responsibilities: [
          "负责公司电商平台的前端页面开发与维护",
          "参与项目需求分析，技术选型，架构设计",
        ],
      },
    ],
    skills: ["HTML/CSS", "JavaScript", "Vue.js", "uni-app"],
    skillDescription:
      "熟练掌握前端开发技术栈，包括HTML/CSS/JavaScript，熟悉Vue.js框架及其生态。",
    selfEvaluation:
      "性格开朗，责任心强，善于团队合作。具有良好的沟通能力和解决问题的能力。",
  };

  // 根据不同的编辑部分初始化表单数据
  if (section.value === "personal") {
    Object.assign(formData, {
      name: resumeData.name,
      gender: resumeData.gender,
      age: resumeData.age,
      phone: resumeData.phone,
      email: resumeData.email,
      jobIntent: resumeData.jobIntent,
      experience: resumeData.experience,
      education: resumeData.education,
      salary: resumeData.salary,
    });
  } else if (section.value === "education") {
    if (action.value === "add") {
      Object.assign(formData, {
        school: "",
        major: "",
        degree: "",
        time: "",
      });
    } else {
      // 编辑现有教育经历
      const eduIndex = index.value >= 0 ? index.value : 0;
      Object.assign(formData, { ...resumeData.educations[eduIndex] });
    }
  } else if (section.value === "experience") {
    if (action.value === "add") {
      Object.assign(formData, {
        company: "",
        title: "",
        time: "",
        responsibilities: [""],
      });
    } else {
      // 编辑现有工作经验
      const expIndex = index.value >= 0 ? index.value : 0;
      Object.assign(formData, { ...resumeData.experiences[expIndex] });
    }
  } else if (section.value === "skills") {
    Object.assign(formData, {
      skills: [...resumeData.skills],
      skillDescription: resumeData.skillDescription,
    });
  } else if (section.value === "evaluation") {
    Object.assign(formData, {
      selfEvaluation: resumeData.selfEvaluation,
    });
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 保存数据
const saveData = () => {
  // 这里应该将数据保存到全局状态或本地存储
  // 为了演示，只显示保存成功提示
  uni.showToast({
    title: "保存成功",
    icon: "success",
    success: () => {
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    },
  });
};

// 选择器事件处理
const onExperienceChange = (e) => {
  formData.experience = experienceOptions[e.detail.value];
};

const onEducationChange = (e) => {
  formData.education = educationOptions[e.detail.value];
};

const onSalaryChange = (e) => {
  formData.salary = salaryOptions[e.detail.value];
};

const onDegreeChange = (e) => {
  formData.degree = educationOptions[e.detail.value];
};

// 工作内容相关方法
const addWorkItem = () => {
  formData.responsibilities.push("");
};

const deleteWorkItem = (index) => {
  if (formData.responsibilities.length > 1) {
    formData.responsibilities.splice(index, 1);
  } else {
    uni.showToast({
      title: "至少保留一项工作内容",
      icon: "none",
    });
  }
};

// 技能相关方法
const addSkill = () => {
  if (newSkill.value.trim()) {
    formData.skills.push(newSkill.value.trim());
    newSkill.value = "";
    showAddSkill.value = false;
  } else {
    uni.showToast({
      title: "技能名称不能为空",
      icon: "none",
    });
  }
};

const deleteSkill = (index) => {
  formData.skills.splice(index, 1);
};
</script>

<style lang="scss" scoped>
.edit-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: calc(120rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

// 导航栏样式
.nav-bar-wrapper {
  background: linear-gradient(
    to right,
    rgba($primary, 0.05),
    rgba($primary, 0.1)
  );
  margin-bottom: 10rpx;

  ::v-deep .uni-nav-bar-text {
    font-weight: 600 !important;
  }
}

.edit-content {
  padding: 20rpx;
  height: calc(100vh - 120rpx - constant(safe-area-inset-bottom));
  height: calc(100vh - 120rpx - env(safe-area-inset-bottom));
}

.edit-form {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: $text-main;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: $text-main;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: $text-main;
}

.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: $text-info;

  &.active {
    color: $primary;
  }

  .radio-icon {
    margin-right: 10rpx;
    color: currentColor;
  }
}

.picker-view {
  width: 100%;
  height: 88rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: $text-main;
}

// 工作内容编辑样式
.work-items {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.work-item-edit {
  display: flex;
  align-items: flex-start;

  .form-textarea {
    flex: 1;
    margin-right: 20rpx;
  }

  .delete-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff4d4f;
    background-color: #fff0f0;
    border-radius: 8rpx;
  }
}

.add-work-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  background-color: rgba($primary, 0.05);
  border: 1rpx dashed rgba($primary, 0.3);
  border-radius: 8rpx;
  color: $primary;
  font-size: 28rpx;

  text:first-child {
    margin-right: 10rpx;
  }
}

// 技能编辑样式
.skills-edit {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.skill-tag-edit {
  display: flex;
  align-items: center;
  background-color: rgba($primary, 0.08);
  color: $primary;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  border: 1rpx solid rgba($primary, 0.1);

  .delete-icon {
    margin-left: 10rpx;
    font-size: 32rpx;
    line-height: 1;
  }
}

.add-skill {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  color: $text-info;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  border: 1rpx dashed #ddd;

  text:first-child {
    margin-right: 6rpx;
  }
}

.skill-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
  gap: 20rpx;
}

.cancel-btn,
.confirm-btn {
  padding: 10rpx 30rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: $text-info;
}

.confirm-btn {
  background-color: $primary;
  color: #fff;
}

// 底部按钮
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
  padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.action-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;

  &:active {
    transform: scale(0.98);
  }
}

.outline-btn {
  margin-right: 20rpx;
  border: 1rpx solid $primary;
  color: $primary;
  background-color: rgba($primary, 0.05);

  &:active {
    background-color: rgba($primary, 0.1);
  }
}

.primary-btn {
  background: linear-gradient(to right, $primary, $primary-500);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba($primary, 0.3);

  &:active {
    box-shadow: 0 2rpx 8rpx rgba($primary, 0.2);
  }
}
</style>
