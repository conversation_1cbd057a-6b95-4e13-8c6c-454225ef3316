---
description: 
globs: 
alwaysApply: true
---
# 样式规范指南

## 全局样式变量

项目使用SCSS预处理器，全局样式变量定义在[src/uni.scss](mdc:src/uni.scss)中：

### 主题颜色

项目使用橙色调作为主题色：

```scss
$primary-50: #fff9ec;
$primary-100: #fff1d3;
$primary-200: #ffdfa5;
$primary-300: #ffc66d;
$primary-400: #ffa232;
$primary-500: #ff860a;
$primary-600: #ff6d00; // 主色调
$primary-700: #cc4e02;
$primary-800: #a13d0b;
$primary-900: #82340c;
$primary-950: #461804;

$primary: $primary-600; // 主题色变量
```

### 文本颜色

```scss
$text-main: #333; // 主要文本颜色
$text-info: #666666; // 信息文本颜色
$text-grey: #999; // 次要文本颜色
$text-placeholder: #808080; // 占位符文本颜色
$text-disable: #c0c0c0; // 禁用状态文本颜色
```

### 间距规范

项目使用8的倍数作为间距规范（rpx单位）：

```scss
$spacing-4: 8rpx;
$spacing-6: 12rpx;
$spacing-8: 16rpx;
$spacing-10: 20rpx;
$spacing-12: 24rpx;
$spacing-14: 28rpx;
$spacing-16: 32rpx;
$spacing-18: 36rpx;
$spacing-20: 40rpx;
$spacing-24: 48rpx;
```

### 圆角规范

```scss
$border-radius-sm: 2px;
$border-radius-base: 3px;
$border-radius-lg: 6px;
$border-radius-circle: 50%;
```

## 样式开发规范

1. **使用SCSS嵌套语法**
   - 限制嵌套层级不超过3层，避免选择器过于复杂
   - 使用`&`符号引用父选择器

2. **文本规范**
   - 主标题：32-36rpx，字重500-600
   - 副标题：28-32rpx，字重400-500
   - 正文文本：26-28rpx，字重400
   - 辅助文本：24rpx，字重400

3. **布局规范**
   - 优先使用flex布局以及uni.scss中预定义的css通用属性
   - 使用UnoCSS原子化类进行快速布局
   - 页面内边距统一使用20rpx
   - 列表项间距统一使用20rpx
   - 内容区块外边距使用20rpx

4. **颜色使用**
   - 内容区块背景色统一使用白色(#ffffff)
   - 页面背景色使用浅灰色(#f5f5f5)
   - 强调内容使用主题色($primary)
   - 背景色根据场景选择合适的背景色，以视觉效果为主，提升用户使用感受
   - 文本颜色根据重要性使用不同层级的文本颜色变量
